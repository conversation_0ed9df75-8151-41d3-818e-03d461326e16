// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		93786B4B2E17C09E001DE20F /* Alamofire in Frameworks */ = {isa = PBXBuildFile; productRef = 93786A742E17BFE5001DE20F /* Alamofire */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		9C6EFFB62DF7D67200ECA016 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 9C6EFF9C2DF7D67100ECA016 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 9C6EFFA32DF7D67100ECA016;
			remoteInfo = CStory;
		};
		9C6EFFC02DF7D67200ECA016 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 9C6EFF9C2DF7D67100ECA016 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 9C6EFFA32DF7D67100ECA016;
			remoteInfo = CStory;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		9C6EFFA42DF7D67100ECA016 /* CStory.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = CStory.app; sourceTree = BUILT_PRODUCTS_DIR; };
		9C6EFFB52DF7D67200ECA016 /* CStoryTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = CStoryTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		9C6EFFBF2DF7D67200ECA016 /* CStoryUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = CStoryUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		9C6EFFC72DF7D67200ECA016 /* Exceptions for "CStory" folder in "CStory" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 9C6EFFA32DF7D67100ECA016 /* CStory */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		9C6EFFA62DF7D67100ECA016 /* CStory */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				9C6EFFC72DF7D67200ECA016 /* Exceptions for "CStory" folder in "CStory" target */,
			);
			path = CStory;
			sourceTree = "<group>";
		};
		9C6EFFB82DF7D67200ECA016 /* CStoryTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = CStoryTests;
			sourceTree = "<group>";
		};
		9C6EFFC22DF7D67200ECA016 /* CStoryUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = CStoryUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		9C6EFFA12DF7D67100ECA016 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				93786B4B2E17C09E001DE20F /* Alamofire in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9C6EFFB22DF7D67200ECA016 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9C6EFFBC2DF7D67200ECA016 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		9C6EFF9B2DF7D67100ECA016 = {
			isa = PBXGroup;
			children = (
				9C6EFFA62DF7D67100ECA016 /* CStory */,
				9C6EFFB82DF7D67200ECA016 /* CStoryTests */,
				9C6EFFC22DF7D67200ECA016 /* CStoryUITests */,
				9C6EFFA52DF7D67100ECA016 /* Products */,
			);
			sourceTree = "<group>";
		};
		9C6EFFA52DF7D67100ECA016 /* Products */ = {
			isa = PBXGroup;
			children = (
				9C6EFFA42DF7D67100ECA016 /* CStory.app */,
				9C6EFFB52DF7D67200ECA016 /* CStoryTests.xctest */,
				9C6EFFBF2DF7D67200ECA016 /* CStoryUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		9C6EFFA32DF7D67100ECA016 /* CStory */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9C6EFFC82DF7D67200ECA016 /* Build configuration list for PBXNativeTarget "CStory" */;
			buildPhases = (
				9C6EFFA02DF7D67100ECA016 /* Sources */,
				9C6EFFA12DF7D67100ECA016 /* Frameworks */,
				9C6EFFA22DF7D67100ECA016 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				9C6EFFA62DF7D67100ECA016 /* CStory */,
			);
			name = CStory;
			packageProductDependencies = (
				93786A742E17BFE5001DE20F /* Alamofire */,
			);
			productName = CStory;
			productReference = 9C6EFFA42DF7D67100ECA016 /* CStory.app */;
			productType = "com.apple.product-type.application";
		};
		9C6EFFB42DF7D67200ECA016 /* CStoryTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9C6EFFCD2DF7D67200ECA016 /* Build configuration list for PBXNativeTarget "CStoryTests" */;
			buildPhases = (
				9C6EFFB12DF7D67200ECA016 /* Sources */,
				9C6EFFB22DF7D67200ECA016 /* Frameworks */,
				9C6EFFB32DF7D67200ECA016 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				9C6EFFB72DF7D67200ECA016 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				9C6EFFB82DF7D67200ECA016 /* CStoryTests */,
			);
			name = CStoryTests;
			packageProductDependencies = (
			);
			productName = CStoryTests;
			productReference = 9C6EFFB52DF7D67200ECA016 /* CStoryTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		9C6EFFBE2DF7D67200ECA016 /* CStoryUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9C6EFFD02DF7D67200ECA016 /* Build configuration list for PBXNativeTarget "CStoryUITests" */;
			buildPhases = (
				9C6EFFBB2DF7D67200ECA016 /* Sources */,
				9C6EFFBC2DF7D67200ECA016 /* Frameworks */,
				9C6EFFBD2DF7D67200ECA016 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				9C6EFFC12DF7D67200ECA016 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				9C6EFFC22DF7D67200ECA016 /* CStoryUITests */,
			);
			name = CStoryUITests;
			packageProductDependencies = (
			);
			productName = CStoryUITests;
			productReference = 9C6EFFBF2DF7D67200ECA016 /* CStoryUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		9C6EFF9C2DF7D67100ECA016 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 2600;
				TargetAttributes = {
					9C6EFFA32DF7D67100ECA016 = {
						CreatedOnToolsVersion = 16.3;
					};
					9C6EFFB42DF7D67200ECA016 = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 9C6EFFA32DF7D67100ECA016;
					};
					9C6EFFBE2DF7D67200ECA016 = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 9C6EFFA32DF7D67100ECA016;
					};
				};
			};
			buildConfigurationList = 9C6EFF9F2DF7D67100ECA016 /* Build configuration list for PBXProject "CStory" */;
			developmentRegion = "zh-Hans";
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
			);
			mainGroup = 9C6EFF9B2DF7D67100ECA016;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				93786A732E17BFE5001DE20F /* XCRemoteSwiftPackageReference "Alamofire" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 9C6EFFA52DF7D67100ECA016 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				9C6EFFA32DF7D67100ECA016 /* CStory */,
				9C6EFFB42DF7D67200ECA016 /* CStoryTests */,
				9C6EFFBE2DF7D67200ECA016 /* CStoryUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		9C6EFFA22DF7D67100ECA016 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9C6EFFB32DF7D67200ECA016 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9C6EFFBD2DF7D67200ECA016 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		9C6EFFA02DF7D67100ECA016 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9C6EFFB12DF7D67200ECA016 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9C6EFFBB2DF7D67200ECA016 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		9C6EFFB72DF7D67200ECA016 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 9C6EFFA32DF7D67100ECA016 /* CStory */;
			targetProxy = 9C6EFFB62DF7D67200ECA016 /* PBXContainerItemProxy */;
		};
		9C6EFFC12DF7D67200ECA016 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 9C6EFFA32DF7D67100ECA016 /* CStory */;
			targetProxy = 9C6EFFC02DF7D67200ECA016 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		9C6EFFC92DF7D67200ECA016 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = CStory/CStory.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = UK45A84643;
				ENABLE_APP_SANDBOX = YES;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SELECTED_FILES = readonly;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = CStory/Info.plist;
				INFOPLIST_KEY_NSCameraUsageDescription = "用于拍摄账单、收据和交易凭证，帮助您快速记录和管理财务信息";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "通过语音输入快速记录交易信息，提升记账效率";
				INFOPLIST_KEY_NSSpeechRecognitionUsageDescription = "通过语音输入快速记录交易信息，提升记账效率";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = cc.nzue.cstory;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator xros xrsimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Debug;
		};
		9C6EFFCA2DF7D67200ECA016 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = CStory/CStory.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = UK45A84643;
				ENABLE_APP_SANDBOX = YES;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SELECTED_FILES = readonly;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = CStory/Info.plist;
				INFOPLIST_KEY_NSCameraUsageDescription = "用于拍摄账单、收据和交易凭证，帮助您快速记录和管理财务信息";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "通过语音输入快速记录交易信息，提升记账效率";
				INFOPLIST_KEY_NSSpeechRecognitionUsageDescription = "通过语音输入快速记录交易信息，提升记账效率";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = cc.nzue.cstory;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator xros xrsimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Release;
		};
		9C6EFFCB2DF7D67200ECA016 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = UK45A84643;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		9C6EFFCC2DF7D67200ECA016 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = UK45A84643;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		9C6EFFCE2DF7D67200ECA016 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = UK45A84643;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MACOSX_DEPLOYMENT_TARGET = 15.3;
				MARKETING_VERSION = 1.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = cc.nzue.CStoryTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/CStory.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/CStory";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Debug;
		};
		9C6EFFCF2DF7D67200ECA016 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = UK45A84643;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MACOSX_DEPLOYMENT_TARGET = 15.3;
				MARKETING_VERSION = 1.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = cc.nzue.CStoryTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/CStory.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/CStory";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Release;
		};
		9C6EFFD12DF7D67200ECA016 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 3;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = UK45A84643;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MACOSX_DEPLOYMENT_TARGET = 15.3;
				MARKETING_VERSION = 1.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = cc.nzue.CStoryUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = CStory;
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Debug;
		};
		9C6EFFD22DF7D67200ECA016 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 3;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = UK45A84643;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MACOSX_DEPLOYMENT_TARGET = 15.3;
				MARKETING_VERSION = 1.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = cc.nzue.CStoryUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = CStory;
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		9C6EFF9F2DF7D67100ECA016 /* Build configuration list for PBXProject "CStory" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9C6EFFCB2DF7D67200ECA016 /* Debug */,
				9C6EFFCC2DF7D67200ECA016 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9C6EFFC82DF7D67200ECA016 /* Build configuration list for PBXNativeTarget "CStory" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9C6EFFC92DF7D67200ECA016 /* Debug */,
				9C6EFFCA2DF7D67200ECA016 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9C6EFFCD2DF7D67200ECA016 /* Build configuration list for PBXNativeTarget "CStoryTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9C6EFFCE2DF7D67200ECA016 /* Debug */,
				9C6EFFCF2DF7D67200ECA016 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9C6EFFD02DF7D67200ECA016 /* Build configuration list for PBXNativeTarget "CStoryUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9C6EFFD12DF7D67200ECA016 /* Debug */,
				9C6EFFD22DF7D67200ECA016 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		93786A732E17BFE5001DE20F /* XCRemoteSwiftPackageReference "Alamofire" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/Alamofire/Alamofire.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.10.2;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		93786A742E17BFE5001DE20F /* Alamofire */ = {
			isa = XCSwiftPackageProductDependency;
			package = 93786A732E17BFE5001DE20F /* XCRemoteSwiftPackageReference "Alamofire" */;
			productName = Alamofire;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 9C6EFF9C2DF7D67100ECA016 /* Project object */;
}
