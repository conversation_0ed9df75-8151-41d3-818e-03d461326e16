# Swift 文档注释规范

基于 Apple 官方文档和 Swift.org 的 API 设计指南整理

## 一、基本语法

### 1. 注释格式
- **单行文档注释**：使用 `///`
- **多行文档注释**：使用 `/** */`
- **普通注释**：使用 `//` 或 `/* */`（不会生成文档）

### 2. 文档结构
```swift
/// 摘要 - 一句话描述功能（必需）
/// 
/// 更详细的描述（可选）
/// 可以包含多个段落
/// 
/// - Parameter name: 参数描述
/// - Returns: 返回值描述
/// - Throws: 可能抛出的错误描述
```

## 二、核心原则

1. **为每个声明编写文档注释** - Swift API 设计指南的关键原则之一
2. **摘要最重要** - 第一段自动成为摘要，在 Xcode Quick Help 中显示
3. **使用 Markdown** - Swift 文档注释支持 CommonMark 格式
4. **保持简洁** - 摘要应该是一个以句号结尾的句子片段

## 三、常用文档标记

### 参数和返回值
- `- Parameter <name>:` 描述单个参数
- `- Parameters:` 描述多个参数（使用缩进列表）
- `- Returns:` 描述返回值
- `- Throws:` 描述可能抛出的错误

### 特殊标记（Xcode 会高亮显示）
- `- Note:` 注意事项
- `- Important:` 重要信息
- `- Warning:` 警告信息
- `- Attention:` 需要注意的地方
- `- Precondition:` 前置条件
- `- Postcondition:` 后置条件
- `- Requires:` 需求说明
- `- Todo:` 待办事项
- `- Bug:` 已知问题
- `- Complexity:` 复杂度说明
- `- SeeAlso:` 相关内容

### 其他标记
- `- Author:` / `- Authors: <AUTHORS>
- `- Copyright:` 版权信息
- `- Date:` 日期
- `- Since:` 可用版本
- `- Version:` 版本信息

## 四、实际示例

### 1. 属性注释
```swift
/// 货币代码（如："USD"、"EUR"）
var code: String

/// 用户的个人资料信息
/// - Note: 首次登录时会自动创建
var profile: UserProfile
```

### 2. 简单方法注释
```swift
/// 将温度从摄氏度转换为华氏度
/// - Parameter celsius: 摄氏温度
/// - Returns: 对应的华氏温度
func convertToFahrenheit(_ celsius: Double) -> Double
```

### 3. 复杂方法注释
```swift
/// 执行网络请求并解析响应
/// 
/// 这个方法会自动处理认证、重试和错误恢复。
/// 支持自定义超时和缓存策略。
/// 
/// - Parameters:
///   - url: 请求的 URL
///   - method: HTTP 方法（默认为 GET）
///   - parameters: 请求参数
///   - headers: 自定义请求头
/// - Returns: 解析后的响应数据
/// - Throws: 
///   - `NetworkError.timeout` 请求超时
///   - `NetworkError.noConnection` 无网络连接
///   - `NetworkError.invalidResponse` 响应格式错误
/// - Note: 这个方法是线程安全的
/// - Complexity: O(1) 不考虑网络延迟
func request<T: Decodable>(
    url: URL,
    method: HTTPMethod = .get,
    parameters: [String: Any]? = nil,
    headers: [String: String]? = nil
) async throws -> T
```

### 4. 类/结构体注释
```swift
/// 管理用户认证状态和会话信息
/// 
/// `AuthenticationManager` 负责处理登录、登出和令牌刷新。
/// 它自动管理认证令牌的存储和更新。
/// 
/// - Important: 必须在应用启动时初始化
/// - SeeAlso: `UserProfile`, `TokenStorage`
class AuthenticationManager {
    // ...
}
```

### 5. 枚举注释
```swift
/// 网络请求可能遇到的错误类型
enum NetworkError: Error {
    /// 请求超时
    case timeout
    /// 无网络连接
    case noConnection
    /// 服务器返回了无效的响应
    /// - Parameter statusCode: HTTP 状态码
    case invalidResponse(statusCode: Int)
}
```

### 6. 协议注释
```swift
/// 定义可缓存对象的要求
/// 
/// 遵循此协议的类型可以被 `CacheManager` 存储和检索。
/// 
/// - Note: 实现时必须确保 `cacheKey` 的唯一性
protocol Cacheable {
    /// 用于在缓存中标识对象的唯一键
    var cacheKey: String { get }
    
    /// 对象在缓存中的过期时间
    /// - Returns: 过期时间间隔（秒）
    func expirationInterval() -> TimeInterval
}
```

## 五、最佳实践

### 1. 摘要编写指南
- 函数/方法：描述它做什么
- 属性/变量：描述它是什么
- 下标：描述它访问什么
- 初始化器：描述它创建什么
- 其他声明：描述声明的实体是什么

### 2. 语言风格
- 使用第三人称描述（"Returns..." 而不是 "Return..."）
- 避免使用"这个方法"、"这个属性"等冗余描述
- 保持语法自然，摘要应该形成一个语法正确的句子

### 3. 代码组织
使用 `// MARK: -` 来组织代码段：
```swift
// MARK: - Properties

// MARK: - Initialization

// MARK: - Public Methods

// MARK: - Private Methods
```

### 4. 何时需要详细文档
- 公共 API 必须有完整文档
- 复杂的内部实现建议添加文档
- 非显而易见的逻辑需要解释
- O(n) 以上复杂度的计算属性应说明复杂度

## 六、工具支持

### 1. Xcode 集成
- **Quick Help**: Option + 点击查看文档
- **自动补全**: 文档会在代码补全时显示
- **Documentation Viewer**: 完整的文档浏览器

### 2. DocC
Apple 的文档编译器，可以：
- 从源代码注释生成文档
- 支持添加文章和教程
- 生成可托管的文档网站

### 3. 第三方工具
- **Jazzy**: 生成 HTML 文档
- **SwiftDoc**: 在线文档生成
- **SourceDocs**: 命令行文档生成工具

## 七、注意事项

1. **避免冗余**：不要重复显而易见的信息
2. **保持更新**：代码修改时同步更新文档
3. **使用示例**：复杂 API 可以在文档中包含代码示例
4. **跨平台考虑**：注明平台特定的行为或限制
5. **版本信息**：使用 `@available` 标注可用性

## 参考资源

- [Swift API Design Guidelines](https://www.swift.org/documentation/api-design-guidelines/)
- [Swift Documentation Comments](https://github.com/apple/swift/blob/main/docs/DocumentationComments.md)
- [Writing Symbol Documentation in Your Source Files](https://developer.apple.com/documentation/xcode/writing-symbol-documentation-in-your-source-files)
- [DocC Documentation](https://www.swift.org/documentation/docc/)