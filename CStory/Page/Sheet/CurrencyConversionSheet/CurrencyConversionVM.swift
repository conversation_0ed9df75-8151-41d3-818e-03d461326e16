//
//  CurrencyConversionVM.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import Foundation
import SwiftUI

// MARK: - 输入字段枚举
enum InputField {
  case none
  case billAmount
  case conversionRate
  case convertAmount
}

// MARK: - 货币转换视图模型
///
/// 负责货币转换界面的业务逻辑处理和状态管理，遵循MVVM架构模式。
/// 该类管理转换金额、汇率计算、数字键盘输入等功能。
///
/// ## 主要职责
/// - 金额和汇率状态管理
/// - 数字键盘输入处理
/// - 货币转换计算逻辑
/// - 格式化和验证功能
///
/// ## 数据流向
/// ```
/// CurrencyConversionView → CurrencyConversionVM → NumberFormatService
/// ```
@MainActor
final class CurrencyConversionVM: ObservableObject {

  // MARK: - Published Properties

  // 发布状态
  @Published var billAmount: String = ""
  @Published var conversionRate: String = "1.0"
  @Published var convertAmount: String = ""
  @Published var convertCurrencyCode: String = ""
  @Published var convertCurrencyCodeSymbol: String = ""

  // 键盘相关状态
  @Published var tempAmount: String = "0"
  @Published var isNumericKeypadVisible: Bool = false
  @Published var activeField: InputField = .none

  // MARK: - Public Methods

  /// 保存数字键盘输入
  func saveNumericInput() {
    switch activeField {
    case .billAmount:
      let oldValue = billAmount
      billAmount = formatAmount(tempAmount)
      if oldValue != billAmount {
        updateConvertAmount()
      }

    case .conversionRate:
      let oldValue = conversionRate
      conversionRate = formatRate(tempAmount)
      if oldValue != conversionRate {
        updateConvertAmount()
      }

    case .convertAmount:
      let oldValue = convertAmount
      convertAmount = formatAmount(tempAmount)
      if oldValue != convertAmount && billAmount != "0" && !billAmount.isEmpty {
        updateConversionRate()
      }

    case .none:
      break
    }

    isNumericKeypadVisible = false
    activeField = .none
  }

  /// 计算并返回换算金额
  func calculateConvertAmount() -> String {
    if let billValue = Double(billAmount),
      let rateValue = Double(conversionRate),
      billValue > 0
    {
      let result = billValue * rateValue
      return formatNumberString(result, precision: 2)
    }
    return "0"
  }

  /// 更新换算金额
  func updateConvertAmount() {
    convertAmount = calculateConvertAmount()
  }

  /// 通用数字格式化（去掉尾部的0和不需要的小数点）
  func formatNumberString(_ value: Double, precision: Int) -> String {
    return NumberFormatService.shared.formatAmountForInput(value, maxDecimals: precision)
  }

  // MARK: - Private Methods

  /// 格式化金额
  private func formatAmount(_ value: String) -> String {
    if let doubleValue = Double(value), doubleValue > 0 {
      return NumberFormatService.shared.formatAmountForInput(doubleValue, maxDecimals: 2)
    }
    return "0"
  }

  /// 格式化汇率
  private func formatRate(_ value: String) -> String {
    if let doubleValue = Double(value), doubleValue > 0 {
      return NumberFormatService.shared.formatExchangeRate(doubleValue, maxDecimals: 6)
    }
    return "1"
  }

  /// 移除尾部的0和不需要的小数点
  private func removeTrailingZeros(_ value: String) -> String {
    var result = value
    if result.contains(".") {
      while result.hasSuffix("0") {
        result.removeLast()
      }
      if result.hasSuffix(".") {
        result.removeLast()
      }
    }
    return result
  }

  /// 根据账单金额和换算金额计算并更新汇率
  private func updateConversionRate() {
    if let billValue = Double(billAmount),
      let convertValue = Double(convertAmount),
      billValue > 0
    {
      let newRate = convertValue / billValue
      conversionRate = formatRate(String(newRate))
    } else if let convertValue = Double(convertAmount),
      convertValue > 0,
      billAmount == "0" || billAmount.isEmpty
    {
      billAmount = "1"
      conversionRate = convertAmount
    }
  }
}
