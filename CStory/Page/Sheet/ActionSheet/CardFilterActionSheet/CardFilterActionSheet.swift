//
//  CardFilterActionSheet.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftUI

/// 卡片筛选操作弹窗
///
/// 提供卡片类型筛选功能，支持显示所有卡片、仅储蓄卡或仅信用卡。
/// 使用统一的选项卡片样式，带有选中状态指示。
/// 使用 MVVM 架构，通过 CardFilterActionSheetVM 管理业务逻辑。
struct CardFilterActionSheet: View {

  // MARK: - ViewModel

  /// 视图模型
  @StateObject private var viewModel: CardFilterActionSheetVM

  // MARK: - 初始化

  /// 初始化卡片筛选弹窗
  /// - Parameters:
  ///   - viewModel: 卡片筛选弹窗视图模型
  init(viewModel: CardFilterActionSheetVM) {
    self._viewModel = StateObject(wrappedValue: viewModel)
  }

  // MARK: - 主体

  var body: some View {
    VStack(spacing: 12) {
      // 标题栏
      SheetTitle(
        title: "筛选卡片",
        button: "xmark.circle.fill",
        rightButtonAction: viewModel.handleDismiss
      )

      // MARK: 筛选选项列表
      ForEach(viewModel.filterOptions, id: \.title) { option in
        SheetRow(
          title: option.title,
          icon: option.icon,
          isSelected: viewModel.isOptionSelected(option)
        ) {
          viewModel.handleOptionTap(option)
        }
      }

      Spacer()
    }

  }
}

// MARK: - 预览代码 (Preview Provider)

#if DEBUG
  struct CardFilterActionSheet_Previews: PreviewProvider {
    static var previews: some View {
      CardFilterActionPreviewContainer()
    }
  }

  struct CardFilterActionPreviewContainer: View {
    @State private var showSheet = false
    @State private var selectedFilter: Bool? = nil

    var body: some View {
      VStack {
        Button("显示卡片筛选菜单") {
          showSheet = true
        }
        .padding()
        .background(.cAccentBlue)
        .foregroundColor(.white)
        .cornerRadius(12)

        if let filter = selectedFilter {
          Text("当前筛选: \(filter ? "信用卡" : "储蓄卡")")
            .padding()
        } else {
          Text("当前筛选: 所有卡片")
            .padding()
        }
      }
      .frame(maxWidth: .infinity, maxHeight: .infinity)
      .background(.cLightBlue)
      .floatingSheet(
        isPresented: $showSheet,
        config: SheetBase(
          maxDetent: .height(200),
          cornerRadius: 24,
          interactiveDimiss: true,
          hPadding: 8,
          bPadding: 4
        )
      ) {
        CardFilterActionSheet(
          viewModel: CardFilterActionSheetVM(
            dataManager: DataManagement(),
            selectedFilter: $selectedFilter,
            dismiss: { showSheet = false },
            onFilterChanged: { filter in
              selectedFilter = filter
              print("筛选条件改变: \(filter?.description ?? "所有卡片")")
            }
          )
        )
      }
    }
  }
#endif
