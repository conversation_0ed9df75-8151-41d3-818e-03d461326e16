//
//  CircleExplanationView.swift
//  CStory
//
//  Created by NZUE on 2025/6/15.
//
import SwiftUI

/// 圆环数据解释视图
struct CircleExplanationSheet: View {
  let savingsRatio: Double
  let creditAvailableRatio: Double
  let cashFlowHealthScore: Double
  var dismiss: () -> Void = {}

  var body: some View {
    VStack(spacing: 12) {
      // 标题栏
      SheetTitle(title: "数据解释", button: "xmark.circle.fill", rightButtonAction: dismiss)

      VStack(spacing: 16) {
        // 外环 - 储蓄占比
        CircleExplanationRow(
          title: "储蓄净值占比",
          subtitle: "储蓄资产净值在总资产中的比例",
          percentage: savingsRatio,
          color: Color.accentColor,
          description: getDescription(for: savingsRatio, type: .savings)
        )

        // 中环 - 信用额度可用率
        CircleExplanationRow(
          title: "信用额度可用率",
          subtitle: "信用卡剩余额度占总额度比例",
          percentage: creditAvailableRatio,
          color: .green,
          description: getDescription(for: creditAvailableRatio, type: .credit)
        )

        // 内环 - 现金流健康度
        CircleExplanationRow(
          title: "现金流健康度",
          subtitle: "近30天收入在总收支中的比例",
          percentage: cashFlowHealthScore,
          color: .orange,
          description: getDescription(for: cashFlowHealthScore, type: .cashFlow)
        )
      }
      .padding(.horizontal, 12)
      Spacer()
    }
  }

  private enum CircleType {
    case savings, credit, cashFlow
  }

  private func getDescription(for ratio: Double, type: CircleType) -> String {
    switch type {
    case .savings:
      if ratio >= 0.7 {
        return "储蓄比例健康，财务稳定性较好"
      } else if ratio >= 0.4 {
        return "储蓄比例适中，建议增加储蓄"
      } else {
        return "储蓄比例偏低，建议控制支出增加储蓄"
      }

    case .credit:
      if ratio >= 0.7 {
        return "信用额度充足，使用率良好"
      } else if ratio >= 0.3 {
        return "信用额度使用适中，注意控制"
      } else {
        return "信用额度使用率偏高，建议及时还款"
      }

    case .cashFlow:
      if ratio >= 0.6 {
        return "现金流健康，收入大于支出"
      } else if ratio >= 0.4 {
        return "现金流一般，收支基本平衡"
      } else {
        return "现金流紧张，支出大于收入"
      }
    }
  }
}

/// 圆环解释行视图
struct CircleExplanationRow: View {
  let title: String
  let subtitle: String
  let percentage: Double
  let color: Color
  let description: String

  var body: some View {
    VStack(alignment: .leading, spacing: 8) {
      HStack {
        // 颜色指示器
        Circle()
          .fill(color)
          .frame(width: 12, height: 12)

        VStack(alignment: .leading, spacing: 2) {
          Text(title)
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(.cBlack)

          Text(subtitle)
            .font(.system(size: 13, weight: .medium))
            .foregroundColor(.cBlack.opacity(0.6))
        }

        Spacer()

        Text("\(Int(percentage * 100))%")
          .font(.system(size: 16, weight: .semibold))
          .foregroundColor(color)
      }

      Text(description)
        .font(.system(size: 13, weight: .medium))
        .foregroundColor(.cBlack.opacity(0.7))
        .padding(.leading, 20)
    }
    .padding(12)
    .background(.cWhite)
    .cornerRadius(16)
    .overlay(
      RoundedRectangle(cornerRadius: 16)
        .strokeBorder(color.opacity(0.2), lineWidth: 1)
    )
  }
}

// MARK: - 预览代码 (Preview Provider)

#if DEBUG
  struct CircleExplanationSheet_Previews: PreviewProvider {
    static var previews: some View {
      CircleExplanationPreviewContainer()
    }
  }

  struct CircleExplanationPreviewContainer: View {
    @State private var showSheet = false

    var body: some View {
      VStack {
        Button("显示圆环数据解释") {
          showSheet = true
        }
        .padding()
        .background(.cAccentBlue)
        .foregroundColor(.white)
        .cornerRadius(12)
      }
      .frame(maxWidth: .infinity, maxHeight: .infinity)
      .background(.cLightBlue)
      .floatingSheet(
        isPresented: $showSheet,
        config: SheetBase(
          maxDetent: .height(400),
          cornerRadius: 24,
          interactiveDimiss: true,
          hPadding: 8,
          bPadding: 4
        )
      ) {
        CircleExplanationSheet(
          savingsRatio: 0.75,
          creditAvailableRatio: 0.80,
          cashFlowHealthScore: 0.65,
          dismiss: { showSheet = false }
        )
      }
    }
  }
#endif
