//
//  SelectCardSheetVM.swift
//  CStory
//
//  Created by NZUE on 2025/8/6.
//

import Foundation
import SwiftUI

/// 卡片选择弹窗 ViewModel
///
/// 管理卡片选择界面的状态和业务逻辑，包括：
/// - 卡片列表展示
/// - 临时选择状态管理
/// - 卡片选择确认
/// - 添加新卡片导航
class SelectCardSheetVM: ObservableObject {

  // MARK: - 属性

  /// 选中的卡片ID
  @Binding var selectedCardId: UUID?
  /// 临时选中的卡片ID
  @Published var tempSelectedCardId: UUID?
  /// 卡片列表
  private let cards: [CardModel]
  /// 数据管理器
  private let dataManager: DataManagement

  // MARK: - 回调

  /// 卡片选择回调
  let onCardSelected: (UUID) -> Void
  /// 取消回调
  let onCancel: () -> Void

  // MARK: - 初始化

  init(
    selectedCardId: Binding<UUID?>,
    cards: [CardModel],
    dataManager: DataManagement,
    onCardSelected: @escaping (UUID) -> Void,
    onCancel: @escaping () -> Void
  ) {
    self._selectedCardId = selectedCardId
    self.cards = cards
    self.dataManager = dataManager
    self.onCardSelected = onCardSelected
    self.onCancel = onCancel

    // 初始化临时选择状态
    self.tempSelectedCardId = selectedCardId.wrappedValue
  }

  // MARK: - 计算属性

  /// 获取可选择的卡片列表（已选中的卡片）
  var selectableCards: [CardModel] {
    return cards.filter { $0.isSelected }
  }

  /// 是否可以确认选择
  var canConfirmSelection: Bool {
    return tempSelectedCardId != nil
  }

  // MARK: - 公共方法

  /// 处理卡片点击
  /// - Parameter card: 被点击的卡片
  @MainActor
  func handleCardTap(_ card: CardModel) {
    dataManager.hapticManager.trigger(.selection)
    print("SelectCardSheet: 点击卡片 \(card.name), ID: \(card.id)")
    print("SelectCardSheet: 更新前 tempSelectedCardId: \(tempSelectedCardId?.uuidString ?? "nil")")
    tempSelectedCardId = card.id
    print("SelectCardSheet: 更新后 tempSelectedCardId: \(tempSelectedCardId?.uuidString ?? "nil")")
  }

  /// 处理取消操作
  @MainActor
  func handleCancel() {
    dataManager.hapticManager.trigger(.impactLight)
    onCancel()
  }

  /// 处理确认选择
  @MainActor
  func handleConfirmSelection() {
    dataManager.hapticManager.trigger(.impactMedium)
    if let cardId = tempSelectedCardId {
      onCardSelected(cardId)
    }
  }

  /// 处理添加新卡片
  @MainActor
  func handleAddNewCard(pathManager: PathManagerHelper) {
    pathManager.path.append(NavigationDestination.cardCategoryView)
  }

  /// 检查卡片是否被选中
  /// - Parameter card: 要检查的卡片
  /// - Returns: 是否被选中
  func isCardSelected(_ card: CardModel) -> Bool {
    return tempSelectedCardId == card.id
  }

  /// 创建CardRowVM
  /// - Parameter card: 卡片模型
  /// - Returns: 配置好的CardRowVM实例
  func createCardRowVM(for card: CardModel) -> CardRowVM {
    return CardRowVM(
      from: card,
      isSelected: isCardSelected(card),
      showTypeTag: true,
      showAdditionalInfo: false,
      onTap: {
        Task { @MainActor in
          self.handleCardTap(card)
        }
      }
    )
  }

  /// 创建AddCardButtonVM
  /// - Parameter pathManager: 路径管理器
  /// - Returns: 配置好的AddCardButtonVM实例
  func createAddCardButtonVM(pathManager: PathManagerHelper) -> AddCardButtonVM {
    return AddCardButtonVM(
      style: .standard,
      action: {
        Task { @MainActor in
          self.handleAddNewCard(pathManager: pathManager)
        }
      },
      shouldDismiss: false  // 在选择卡片界面中不自动关闭 sheet
    )
  }
}
