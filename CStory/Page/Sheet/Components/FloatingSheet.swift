//
//  FloatingSheet.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftUI

// MARK: - Sheet 配置基础类

/// Sheet 配置类
///
/// 定义浮动弹窗的基本配置参数，包括高度、圆角、交互等。
struct SheetBase {
  /// 最大高度
  var maxDetent: PresentationDetent
  /// 圆角半径
  var cornerRadius: CGFloat = 16
  /// 是否禁用交互式关闭
  var interactiveDimiss: Bool = false
  /// 水平内边距
  var hPadding: CGFloat = 16
  /// 底部内边距
  var bPadding: CGFloat = 12
}

extension View {
  /// 浮动弹窗修饰符
  ///
  /// 为视图添加统一样式的底部浮动弹窗功能。
  /// 弹窗具有半透明背景、圆角、内边距等统一样式。
  ///
  /// ## 使用示例
  /// ```swift
  /// .floatingSheet(
  ///     isPresented: $showSheet,
  ///     config: SheetBase(maxDetent: .fraction(0.5))
  /// ) {
  ///     ContentView()
  /// }
  /// ```
  ///
  /// - Parameters:
  ///   - isPresented: 控制弹窗显示/隐藏的绑定状态
  ///   - config: 弹窗配置，默认为几乎全屏高度
  ///   - content: 弹窗内容视图
  @ViewBuilder
  func floatingSheet<Content: View>(
    isPresented: Binding<Bool>,
    config: SheetBase = .init(maxDetent: .fraction(0.99)),
    @ViewBuilder content: @escaping () -> Content
  ) -> some View {
    self
      .sheet(isPresented: isPresented) {
        content()
          // 半透明材质背景
          .background(.regularMaterial)
          // 圆角裁剪
          .clipShape(.rect(cornerRadius: config.cornerRadius))
          // 内边距
          .padding(.horizontal, config.hPadding)
          .padding(.bottom, config.bPadding)
          // 底部对齐
          .frame(maxHeight: .infinity, alignment: .bottom)
          // 高度设置
          .presentationDetents([config.maxDetent])
          // 无圆角（因为我们自定义了圆角）
          .presentationCornerRadius(16)
          // 透明背景
          .presentationBackground(.clear)
          // 隐藏拖动指示器
          .presentationDragIndicator(.hidden)
          // 交互式关闭控制
          .interactiveDismissDisabled(config.interactiveDimiss)
      }
  }
}
