//
//  SheetRow.swift
//  CStory
//
//  Created by NZUE on 2025/8/12.
//

import SwiftUI

// MARK: - 弹窗行组件

/// 弹窗行组件
///
/// 可复用的弹窗选项行，支持图标、文字、危险操作样式、选中状态
/// 可以在各种 Sheet 中独立使用
///
/// 使用示例：
/// ```swift
/// SheetRow(
///   title: "删除",
///   icon: "trash",
///   isDestructive: true
/// ) {
///   // 删除操作
/// }
/// ```
struct SheetRow: View {

  // MARK: - 属性

  let title: String
  let icon: String
  let isDestructive: Bool
  let isSelected: Bool
  let action: () -> Void

  // MARK: - 初始化

  /// 基础版本
  init(
    title: String,
    icon: String,
    isDestructive: Bool = false,
    action: @escaping () -> Void
  ) {
    self.title = title
    self.icon = icon
    self.isDestructive = isDestructive
    self.isSelected = false
    self.action = action
  }

  /// 带选中状态版本（用于筛选类操作）
  init(
    title: String,
    icon: String,
    isSelected: Bool,
    action: @escaping () -> Void
  ) {
    self.title = title
    self.icon = icon
    self.isDestructive = false
    self.isSelected = isSelected
    self.action = action
  }

  // MARK: - 视图

  var body: some View {
    HStack(spacing: 12) {
      // 图标
      Image(systemName: icon)
        .font(.system(size: 20))
        .foregroundColor(
          isDestructive ? .white : isSelected ? .white : .cAccentBlue
        )
        .frame(width: 44, height: 44)
        .background(
          isDestructive
            ? .cAccentRed : isSelected ? .cAccentBlue : .cAccentBlue.opacity(0.1)
        )
        .cornerRadius(12)

      // 文字
      Text(title)
        .font(.system(size: 14, weight: .medium))
        .foregroundColor(
          isDestructive ? .cAccentRed : isSelected ? .cAccentBlue : .cBlack
        )

      Spacer()

    }
    .padding(4)
    .background(.cWhite)
    .cornerRadius(16)
    .overlay(
      RoundedRectangle(cornerRadius: 16)
        .strokeBorder(
          isDestructive
            ? .cAccentRed.opacity(0.2)
            : isSelected ? .cAccentBlue : .cAccentBlue.opacity(0.08),
          lineWidth: 1
        )
    )
    .padding(.horizontal, 16)
    .onTapGesture {
      action()
    }
  }
}

// MARK: - 预览

#if DEBUG
  struct SheetRow_Previews: PreviewProvider {
    static var previews: some View {
      VStack(spacing: 12) {
        // 普通选项
        SheetRow(
          title: "编辑",
          icon: "pencil"
        ) {
          print("编辑")
        }

        // 带选中状态的选项
        SheetRow(
          title: "所有卡片",
          icon: "creditcard.fill",
          isSelected: true
        ) {
          print("选中")
        }

        // 危险操作
        SheetRow(
          title: "删除",
          icon: "trash",
          isDestructive: true
        ) {
          print("删除")
        }

        Spacer()
      }
      .padding()
      .background(.cLightBlue)
    }
  }
#endif
