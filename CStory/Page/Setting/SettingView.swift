//
//  SettingView.swift
//  CStory
//
//  Created by NZUE on 2025/3/13.
//

import SwiftUI

/// 设置页面主视图
///
/// 应用的设置页面，采用MVVM架构模式。
/// 提供记账设置、数据安全、关于等功能的统一入口。
///
/// ## 主要功能
/// - 分类管理和货币汇率设置
/// - 数据安全相关功能
/// - 应用信息和法律条款
/// - 隐藏的开发者测试功能
///
/// ## 架构特点
/// - 使用 SettingViewVM 管理业务逻辑
/// - 组件化的设置项渲染
/// - 统一的交互和导航处理
/// - 支持弹窗内部导航，无需关闭整个设置弹窗
///
/// - Author: 咩咩
/// - Since: 2025.7.25
/// - Note: 该视图专注于UI渲染，业务逻辑由ViewModel处理
struct SettingView: View {

  // MARK: - Environment Properties

  @Environment(\.dismiss) private var dismiss
  @EnvironmentObject private var pathManager: PathManagerHelper

  // MARK: - State Properties

  /// 设置页面视图模型
  @ObservedObject private var viewModel: SettingVM

  /// 弹窗内部导航路径
  @State private var settingNavigationPath = NavigationPath()

  // MARK: - Initialization

  init(viewModel: SettingVM) {
    self.viewModel = viewModel
  }

  // MARK: - Body

  var body: some View {
    NavigationStack(path: $settingNavigationPath) {
      VStack(spacing: 0) {
        // MARK: 标题栏
        titleBar

        // MARK: 主要内容区域
        mainContent
      }
      .navigationDestination(for: NavigationDestination.self) { destination in
        // 在弹窗内部显示设置子页面
        destination.settingDestinationView()
      }
    }
    .sheet(isPresented: $viewModel.showTestView) {
      TestView()
    }
  }

  // MARK: - Private Views

  /// 标题栏视图
  private var titleBar: some View {
    ZStack(alignment: .top) {
      // 渐变模糊背景
      VariableBlurView(
        maxBlurRadius: 15,
        direction: .blurredTopClearBottom,
        startOffset: -0.05
      )
      .ignoresSafeArea()
      .frame(height: 60)

      // 标题栏内容
      HStack {
        Text("设置")
          .font(.system(size: 28, weight: .medium))
          .foregroundColor(.cBlack)

        Spacer()

        Button(action: {
          dismiss()
        }) {
          Image(systemName: "xmark.circle.fill")
            .foregroundColor(.cBlack.opacity(0.4))
            .font(.system(size: 20, weight: .medium))
        }
      }
      .padding(16)
    }
    .zIndex(1)
  }

  /// 主要内容视图
  private var mainContent: some View {
    VStack(spacing: 12) {
      // MARK: 设置分组列表
      VStack(spacing: 24) {
        ForEach(viewModel.settingSections, id: \.title) { section in
          VStack(spacing: 4) {
            // 分组标题
            Text(section.title)
              .frame(maxWidth: .infinity, alignment: .leading)
              .font(.system(size: 14, weight: .medium))
              .foregroundColor(.cBlack.opacity(0.4))

            // 设置项列表
            VStack(spacing: 0) {
              ForEach(section.items, id: \.id) { item in
                SettingRow(
                  viewModel: SettingRowVM(
                    from: item,
                    onTap: {
                      viewModel.handleSettingItemTapInSheet(
                        item,
                        settingNavigationPath: $settingNavigationPath
                      )
                    }
                  )
                )
              }
            }
          }
        }
      }
      .padding(.horizontal, 16)
      .padding(.vertical, 12)

      Spacer()

      // MARK: 底部版本信息和隐藏功能
      bottomSection
    }
  }

  /// 底部区域视图
  private var bottomSection: some View {
    HStack {
      // 左侧隐藏点击区域
      Rectangle()
        .fill(.clear)
        .contentShape(Rectangle())
        .frame(width: 50, height: 50)
        .onTapGesture {
          viewModel.handleSecretTap("left")
        }

      Spacer()
      VStack(spacing: 2) {
        // 备案号
        Text("浙ICP备2024135033号-2A")

        // 版本信息
        Text(viewModel.appVersionText)

      }
      .font(.system(size: 12, weight: .regular))
      .foregroundColor(.cBlack.opacity(0.3))
      .padding(.bottom, 20)
      Spacer()

      // 右侧隐藏点击区域
      Rectangle()
        .fill(.clear)
        .contentShape(Rectangle())
        .frame(width: 50, height: 50)
        .onTapGesture {
          viewModel.handleSecretTap("right")
        }
    }
  }
}
#if DEBUG
  struct SettingView_Previews: PreviewProvider {
    static var previews: some View {
      SettingView(viewModel: SettingVM(dataManager: DataManagement()))
    }
  }
#endif
