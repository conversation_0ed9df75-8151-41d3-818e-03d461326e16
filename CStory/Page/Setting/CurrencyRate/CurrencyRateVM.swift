//
//  CurrencyRateVM.swift
//  CStory
//
//  Created by NZUE on 2025/1/23.
//

import Alamofire
import SwiftData
import SwiftUI

/// 货币汇率管理视图的ViewModel
///
/// 负责管理货币汇率界面的业务逻辑和状态管理，包括：
/// - 汇率数据的获取和更新
/// - 货币选择和操作
/// - 本位币设置
/// - 汇率去重和批量操作
@MainActor
class CurrencyRateVM: ObservableObject {

  // MARK: - Published Properties

  /// 是否正在加载汇率数据
  @Published var isLoadingRates: Bool = false
  /// 显示货币操作sheet
  @Published var showCurrencySheet: Bool = false
  /// 当前选中的货币
  @Published var selectedCurrency: CurrencyModel?
  /// 显示本位币选择sheet
  @Published var showBaseCurrencySheet: Bool = false
  /// 控制全选/取消全选状态
  @Published var isSelectAll: Bool = false
  /// 搜索文本
  @Published var searchText: String = ""

  // MARK: - Dependencies

  /// 数据管理器，提供统一的数据访问接口
  private let dataManager: DataManagement
  /// SwiftData 模型上下文，用于数据持久化
  private let modelContext: ModelContext

  // MARK: - Initialization

  /// 初始化货币汇率视图模型
  ///
  /// - Parameters:
  ///   - dataManager: 数据管理器
  ///   - modelContext: SwiftData模型上下文
  init(dataManager: DataManagement, modelContext: ModelContext) {
    self.dataManager = dataManager
    self.modelContext = modelContext
  }

  // MARK: - Computed Properties

  /// 从UserDefaults获取本位币代码
  var baseCurrencyCode: String {
    UserDefaults.standard.string(forKey: "baseCurrencyCode") ?? "CNY"
  }

  /// 检查是否存在重复的货币
  func hasDuplicates(from dataManager: DataManagement) -> Bool {
    let allCurrencies = dataManager.currencies
    var uniqueCodes = Set<String>()

    for currency in allCurrencies {
      if !uniqueCodes.insert(currency.code).inserted {
        return true
      }
    }
    return false
  }

  /// 处理本位币选择
  func handleBaseCurrencySelection(_ newBaseCurrency: CurrencyModel) {
    do {
      try newBaseCurrency.setAsBaseCurrency(in: modelContext)
      dataManager.hapticManager.trigger(.impactMedium)
    } catch {
      print("设置本位币失败: \(error)")
      dataManager.hapticManager.trigger(.error)
    }
  }

  /// 保存自定义汇率
  func saveCustomRate(
    for currency: CurrencyModel, customRateText: String, onSuccess: @escaping () -> Void
  ) {
    guard !currency.isBaseCurrency,
      let newRate = Double(customRateText)
    else { return }

    // 确保汇率有效
    guard newRate > 0 else { return }

    currency.customRate = newRate
    currency.rate = newRate
    currency.isCustom = true
    currency.updatedAt = Date()
    do {
      try modelContext.save()
      dataManager.hapticManager.trigger(.impactMedium)
      onSuccess()
    } catch {
      print("保存自定义汇率失败: \(error)")
      dataManager.hapticManager.trigger(.error)
    }
  }

  /// 恢复默认汇率
  func restoreDefaultRate(for currency: CurrencyModel, onSuccess: @escaping () -> Void) {
    currency.restoreDefaultRate()
    do {
      try modelContext.save()
      dataManager.hapticManager.trigger(.impactMedium)
      onSuccess()
    } catch {
      print("恢复默认汇率失败: \(error)")
      dataManager.hapticManager.trigger(.error)
    }
  }

  // MARK: - Computed Properties

  /// 过滤后的货币列表
  func filteredCurrencies(from dataManager: DataManagement) -> [CurrencyModel] {
    let nonBaseCurrencies = dataManager.currencies.filter { !$0.isBaseCurrency }

    if searchText.isEmpty {
      return nonBaseCurrencies.sorted(by: { $0.order < $1.order })
    } else {
      return nonBaseCurrencies.filter { currency in
        currency.name.localizedCaseInsensitiveContains(searchText)
          || currency.code.localizedCaseInsensitiveContains(searchText)
          || currency.symbol.localizedCaseInsensitiveContains(searchText)
      }.sorted(by: { $0.order < $1.order })
    }
  }

  /// 全选按钮标题
  func selectAllButtonTitle(from dataManager: DataManagement) -> String {
    let nonBaseCurrencies = dataManager.currencies.filter { !$0.isBaseCurrency }
    let allSelected = !nonBaseCurrencies.isEmpty && nonBaseCurrencies.allSatisfy { $0.isSelected }
    return allSelected ? "取消全选" : "选择全部"
  }

  // MARK: - Actions

  /// 初始化时检查本位币状态
  func checkBaseCurrency(using modelContext: ModelContext, dataManager: DataManagement) {
    // 如果 UserDefaults 中有本位币设置，但数据库中没有对应的本位币，尝试恢复
    if let currency = dataManager.currencies.first(where: { $0.code == baseCurrencyCode }),
      dataManager.currencies.filter({ $0.isBaseCurrency }).isEmpty
    {
      do {
        try currency.setAsBaseCurrency(in: modelContext)
        print("已从 UserDefaults 恢复本位币设置：\(baseCurrencyCode)")
      } catch {
        print("恢复本位币设置失败：\(error)")
      }
    }

    // 更新全选状态
    updateSelectAllState(dataManager: dataManager)
  }

  /// 显示本位币选择
  func showBaseCurrencySelection() {
    dataManager.hapticManager.trigger(.selection)
    showBaseCurrencySheet = true
  }

  /// 显示货币操作
  func showCurrencyOperations(currency: CurrencyModel) {
    dataManager.hapticManager.trigger(.selection)
    selectedCurrency = currency
    showCurrencySheet = true
  }

  /// 更新全选状态
  func updateSelectAllState(dataManager: DataManagement) {
    let nonBaseCurrencies = dataManager.currencies.filter { !$0.isBaseCurrency }
    let newSelectAllState =
      !nonBaseCurrencies.isEmpty && nonBaseCurrencies.allSatisfy { $0.isSelected }

    if isSelectAll != newSelectAllState {
      isSelectAll = newSelectAllState
    }
  }

  /// 切换全选/取消全选状态
  func toggleSelectAll(using modelContext: ModelContext, dataManager: DataManagement) {
    dataManager.hapticManager.trigger(.selection)

    // 先更新当前状态
    updateSelectAllState(dataManager: dataManager)

    // 根据当前状态决定操作：如果当前是全选状态，则取消全选；否则全选
    let targetState = !isSelectAll

    // 更新所有非本位币的货币选中状态
    for currency in dataManager.currencies.filter({ !$0.isBaseCurrency }) {
      currency.isSelected = targetState
    }

    // 更新状态
    isSelectAll = targetState

    // 批量更新后一次性保存
    try? modelContext.save()
  }

  /// 获取最新汇率数据
  func fetchLatestExchangeRates(using modelContext: ModelContext, dataManager: DataManagement) {
    guard !isLoadingRates else { return }

    dataManager.hapticManager.trigger(.impactLight)
    isLoadingRates = true
    print("开始从远程API获取最新汇率数据...")

    let apiURL = "https://exchange.nzue.cc/api/exchange-rates"

    Task { @MainActor in
      do {
        let data = try await AF.request(apiURL)
          .validate()
          .serializingData()
          .value

        // 解析JSON数据
        let decoder = JSONDecoder()
        let exchangeRates = try decoder.decode(ExchangeRateAPIResponse.self, from: data)

        // 打印API返回的数据
        print("远程汇率数据获取成功!")
        print("基准货币: \(exchangeRates.data.base)")
        print("更新时间: \(exchangeRates.data.last_updated)")

        // 更新本地汇率数据
        self.updateLocalCurrencyRates(
          with: exchangeRates.data.rates, using: modelContext, dataManager: dataManager)
        self.isLoadingRates = false

      } catch {
        print("获取汇率数据失败: \(error.localizedDescription)")
        // 远程API获取失败，尝试从本地JSON文件获取
        self.fetchFromLocalJSON(using: modelContext, dataManager: dataManager)
        self.isLoadingRates = false
      }
    }
  }

  /// 移除重复的货币数据
  func removeDuplicates(using modelContext: ModelContext, dataManager: DataManagement) {
    dataManager.hapticManager.trigger(.impactMedium)

    // 获取所有货币
    let allCurrencies = dataManager.currencies

    // 按货币代码分组，找出重复项
    let groupedCurrencies = Dictionary(grouping: allCurrencies) { $0.code }
    var totalRemoved = 0

    // 处理每组重复的货币
    for (code, currencies) in groupedCurrencies {
      guard currencies.count > 1 else { continue }

      // 按创建时间排序，保留最早创建的
      let sortedCurrencies = currencies.sorted { $0.createdAt < $1.createdAt }
      let toRemove = Array(sortedCurrencies.dropFirst())

      // 删除重复的货币
      for currency in toRemove {
        modelContext.delete(currency)
        totalRemoved += 1
      }

      print("货币 \(code): 保留最早创建的，删除 \(toRemove.count) 个重复项")
    }

    // 保存更改
    do {
      try modelContext.save()
      if totalRemoved > 0 {
        print("✅ 货币去重完成：删除了 \(totalRemoved) 个重复货币")
      } else {
        print("✅ 货币去重完成：未发现重复数据")
      }
    } catch {
      print("❌ 货币去重失败: \(error.localizedDescription)")
    }
  }

  // MARK: - Private Methods

  /// 从本地JSON文件获取汇率数据（作为备用方案）
  private func fetchFromLocalJSON(using modelContext: ModelContext, dataManager: DataManagement) {
    print("尝试从本地JSON获取汇率数据...")

    guard let fileURL = Bundle.main.url(forResource: "exchange_rates", withExtension: "json") else {
      print("无法找到exchange_rates.json文件")
      return
    }

    do {
      // 读取文件内容
      let jsonData = try Data(contentsOf: fileURL)

      // 解析JSON数据
      let decoder = JSONDecoder()
      let exchangeRates = try decoder.decode(ExchangeRateAPIResponse.self, from: jsonData)

      // 打印本地JSON数据信息
      print("本地汇率数据加载成功!")
      print("基准货币: \(exchangeRates.data.base)")
      print("更新时间: \(exchangeRates.data.last_updated)")

      // 更新本地汇率数据
      updateLocalCurrencyRates(
        with: exchangeRates.data.rates, using: modelContext, dataManager: dataManager)
    } catch {
      print("读取或解析本地JSON数据失败: \(error.localizedDescription)")
    }
  }

  /// 更新本地货币汇率数据
  private func updateLocalCurrencyRates(
    with rates: [String: Double], using modelContext: ModelContext, dataManager: DataManagement
  ) {
    // 确保有本位币
    guard let baseCurrency = dataManager.currencies.first(where: { $0.isBaseCurrency }) else {
      print("更新汇率失败: 没有设置本位币")
      return
    }

    // 遍历所有货币并更新汇率
    for currency in dataManager.currencies {
      if let apiRate = rates[currency.code] {
        // API返回的汇率是"1CNY=多少外币"的形式
        // 但我们需要"1外币=多少CNY"的形式，所以需要取倒数

        // 如果本位币不是CNY，需要进行转换
        if baseCurrency.code != "CNY" {
          // 获取本位币相对于CNY的汇率
          if let cnyToBaseRate = rates[baseCurrency.code] {
            // 计算相对于本位币的汇率:
            // 1外币 = ? 本位币
            // = (1/apiRate) * (cnyToBaseRate) = cnyToBaseRate/apiRate
            if apiRate > 0 {
              let calculatedRate = cnyToBaseRate / apiRate
              // 确保保留6位小数精度
              let newRate = (calculatedRate * 1_000_000).rounded() / 1_000_000
              // 更新默认汇率
              currency.defaultRate = newRate
              // 如果没有自定义汇率，则更新当前汇率
              if currency.customRate == nil {
                currency.rate = newRate
              }
            }
          }
        } else {
          // 如果本位币是CNY，只需要将API返回的汇率取倒数
          if apiRate > 0 {
            let inverseRate = 1.0 / apiRate
            // 确保保留6位小数精度
            let newRate = (inverseRate * 1_000_000).rounded() / 1_000_000
            // 更新默认汇率
            currency.defaultRate = newRate
            // 如果没有自定义汇率，则更新当前汇率
            if currency.customRate == nil {
              currency.rate = newRate
            }
          }
        }

        // 更新时间戳
        currency.updatedAt = Date()
      }
    }

    // 保存变更
    do {
      try modelContext.save()
      print("本地货币汇率更新成功")
    } catch {
      print("保存更新后的汇率失败: \(error)")
    }
  }
}
