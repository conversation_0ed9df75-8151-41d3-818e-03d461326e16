//
//  SettingRow.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/25.
//

import SwiftUI

/// 设置行组件
///
/// 用于显示设置页面中的单个设置项，包括图标、标题和箭头指示器。
/// 支持不同类型的设置项样式，如普通设置和危险操作。
/// 遵循MVVM架构模式，通过SettingRowVM处理所有数据格式化和业务逻辑。
///
/// ## 主要功能
/// - 统一的设置项视觉样式
/// - 支持危险操作的特殊样式
/// - 可配置的图标和标题
/// - 标准的点击交互
///
/// ## 使用示例
/// ```swift
/// let viewModel = SettingRowVM(
///   icon: "category_icon",
///   title: "分类管理",
///   isDangerous: false
/// )
/// viewModel.onTap = { /* 处理点击事件 */ }
/// SettingRow(viewModel: viewModel)
/// ```
///
/// - Author: 咩咩
/// - Since: 2025.7.25
/// - Note: 该组件是无状态的，所有状态管理都在ViewModel中
struct SettingRow: View {

  // MARK: - Properties

  /// 设置行视图模型
  ///
  /// 管理设置行显示数据和交互逻辑的视图模型实例。
  /// 包含图标、标题、危险状态等所有显示相关数据。
  /// 视图通过观察此对象的变化来自动更新UI。
  @ObservedObject var viewModel: SettingRowVM

  // MARK: - Body

  var body: some View {
    Button(action: {
      viewModel.onTap?()
    }) {
      HStack {
        // MARK: 左侧内容（图标和标题）
        HStack(spacing: 12) {
          // 图标容器
          Image(viewModel.icon)
            .foregroundColor(.cBlack)
            .frame(width: 28, height: 28)
            .background(.cWhite)
            .cornerRadius(6)
            .overlay(
              RoundedRectangle(cornerRadius: 6)
                .strokeBorder(.cBlack.opacity(0.08), lineWidth: 1)
            )

          // 标题文本
          Text(viewModel.title)
            .font(.system(size: 15, weight: .medium))
            .foregroundColor(viewModel.isDangerous ? .red : .cBlack)
        }

        Spacer()

        // MARK: 右侧箭头指示器
        Image(systemName: "chevron.forward")
          .font(.system(size: 12, weight: .medium))
          .foregroundColor(.cBlack.opacity(0.6))
      }
      .padding(.vertical, 12)
    }
  }
}
