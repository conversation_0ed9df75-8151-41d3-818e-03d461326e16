//
//  CustomImagePicker.swift
//  CStory
//
//  Created by <PERSON><PERSON> on 2023/4/12.
//

import ImageIO
import MobileCoreServices
import PhotosUI
import SwiftUI

@available(iOS 16.0, *)
extension View {
  func cropImagePicker(show: Binding<Bool>, croppedImage: Binding<UIImage?>) -> some View {
    CustomImagePicker(show: show, croppedImage: croppedImage) {
      self
    }
  }

}

@available(iOS 16.0, *)
private struct CustomImagePicker<Content: View & Sendable>: View {
  @Binding var show: Bool
  @Binding var croppedImage: UIImage?
  var content: Content

  init(show: Binding<Bool>, croppedImage: Binding<UIImage?>, content: @escaping () -> Content) {
    self._show = show
    self._croppedImage = croppedImage
    self.content = content()
  }

  @State var photosItem: PhotosPickerItem?
  @State var selectedImage: UIImage?
  @State private var showCropView: Bool = false

  var body: some View {
    content
      .photosPicker(isPresented: $show, selection: $photosItem)
      .onChange(of: photosItem) { _, newValue in
        if let value = newValue {
          Task {
            if let imageData = try? await value.loadTransferable(type: Data.self),
              let image = UIImage(data: imageData)
            {
              await MainActor.run(body: {
                selectedImage = image
                showCropView.toggle()
                // 清空选择项，确保下次选择时是全新的
                photosItem = nil
              })
            }
          }
        }
      }

      .fullScreenCover(isPresented: $showCropView) {
        selectedImage = nil
      } content: {
        CropViewWrapper(
          selectedImage: $selectedImage,
          croppedImage: $croppedImage
        )
      }
  }
}

@available(iOS 16.0, *)
struct CropViewWrapper: View {
  @Binding var selectedImage: UIImage?
  @Binding var croppedImage: UIImage?

  var body: some View {
    if let image = selectedImage {
      CropView(image: image) { croppedImage, status in
        if let croppedImage {
          self.croppedImage = croppedImage
        }
      }
    } else {
      // 等待图片加载的占位视图
      Color.black
        .overlay(
          ProgressView()
            .progressViewStyle(CircularProgressViewStyle(tint: .white))
        )
        .ignoresSafeArea()
    }
  }
}

@available(iOS 16.0, *)
struct CropView: View {
  var image: UIImage?
  var onCrop: (UIImage?, Bool) -> Void

  // 固定为正方形裁切
  private let cropSize: CGFloat = 300

  @Environment(\.dismiss) var dismiss

  //MARK: 图片操作相关
  @State private var scale: CGFloat = 1
  @State private var lastScale: CGFloat = 0
  @State private var offset: CGSize = .zero
  @State private var lastOffset: CGSize = .zero
  @GestureState private var isInteracting: Bool = false

  var body: some View {
    NavigationStack {
      imageView()
        .navigationTitle("选择图片")
        .navigationBarTitleDisplayMode(.inline)
        .toolbarBackground(.visible, for: .navigationBar)
        .toolbarBackground(Color.black, for: .navigationBar)
        .toolbarColorScheme(.dark, for: .navigationBar)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background {
          Color.cBlack
            .ignoresSafeArea()
        }
        .toolbar {
          ToolbarItem(placement: .navigationBarLeading) {
            Button {
              dismiss()
            } label: {
              Image(systemName: "xmark")
            }
          }

          ToolbarItem(placement: .navigationBarTrailing) {
            Button {
              let renderer = ImageRenderer(content: imageView(true))
              renderer.proposedSize = .init(width: cropSize, height: cropSize)
              if let uiimage = renderer.uiImage {
                // 对图片进行压缩处理
                let compressedImage = compressImage(uiimage, quality: 0.8)
                onCrop(compressedImage, true)
              } else {
                onCrop(nil, false)
              }
              dismiss()
            } label: {
              Image(systemName: "checkmark")
            }
          }
        }
    }
  }

  // HEIF图片压缩函数
  private func compressImage(_ image: UIImage, quality: CGFloat) -> UIImage {
    // 获取原始图片数据大小用于对比
    let originalSize = getImageDataSize(image)
    print("📷 图片压缩开始 - 原始大小: \(String(format: "%.2f", Double(originalSize) / 1024.0)) KB")

    // 尝试HEIF压缩
    guard let heifData = convertImageToHEIF(image: image, quality: quality) else {
      print("❌ HEIF压缩失败，返回原图")
      return image
    }

    // 创建压缩后的图片
    guard let compressedImage = UIImage(data: heifData) else {
      print("❌ HEIF数据转换为UIImage失败，返回原图")
      return image
    }

    // 打印压缩对比信息
    let compressedSize = heifData.count
    let compressionRatio = (1.0 - Double(compressedSize) / Double(originalSize)) * 100.0

    print("✅ HEIF压缩完成")
    print("📊 压缩后大小: \(String(format: "%.2f", Double(compressedSize) / 1024.0)) KB")
    print("📈 压缩率: \(String(format: "%.1f", compressionRatio))%")
    print("💾 节省空间: \(String(format: "%.2f", Double(originalSize - compressedSize) / 1024.0)) KB")

    return compressedImage
  }

  // 获取图片数据大小的辅助函数
  private func getImageDataSize(_ image: UIImage) -> Int {
    // 尝试获取PNG数据大小（保持透明度）
    if let pngData = image.pngData() {
      return pngData.count
    }
    // 如果PNG失败，使用高质量JPEG作为参考
    if let jpegData = image.jpegData(compressionQuality: 1.0) {
      return jpegData.count
    }
    return 0
  }

  // HEIF格式转换函数
  private func convertImageToHEIF(image: UIImage, quality: CGFloat) -> Data? {
    let imageData = NSMutableData()

    guard
      let imageDestination = CGImageDestinationCreateWithData(
        imageData,
        AVFileType.heic as CFString,
        1,
        nil
      ), let originalCGImage = image.cgImage
    else {
      return nil
    }

    // 检查是否真正需要Alpha通道（检测是否有透明像素）
    let needsAlpha = imageHasTransparentPixels(cgImage: originalCGImage)

    var cgImageToUse = originalCGImage

    // 如果不需要Alpha通道，重新创建不带Alpha的CGImage
    if !needsAlpha {
      if let opaqueImage = createOpaqueImage(from: originalCGImage) {
        cgImageToUse = opaqueImage
      }
    }

    let options: [CFString: Any] = [
      kCGImageDestinationLossyCompressionQuality: quality
    ]

    CGImageDestinationAddImage(imageDestination, cgImageToUse, options as CFDictionary)

    guard CGImageDestinationFinalize(imageDestination) else {
      return nil
    }

    return imageData as Data
  }

  /// 检查图片是否有透明像素
  private func imageHasTransparentPixels(cgImage: CGImage) -> Bool {
    // 首先检查Alpha信息
    let alphaInfo = cgImage.alphaInfo

    // 如果明确没有Alpha通道，直接返回false
    if alphaInfo == .none || alphaInfo == .noneSkipFirst || alphaInfo == .noneSkipLast {
      return false
    }

    // 对于有Alpha信息的图片，进行实际的透明像素检测
    return hasActualTransparentPixels(cgImage: cgImage)
  }

  /// 通过采样检测图片是否真正包含透明像素
  private func hasActualTransparentPixels(cgImage: CGImage) -> Bool {
    let width = cgImage.width
    let height = cgImage.height

    // 对于小图片，检查更多采样点；对于大图片，使用较少采样点以提高性能
    let sampleStep = max(1, min(width, height) / 20)  // 每边最多20个采样点

    guard let dataProvider = cgImage.dataProvider,
      let data = dataProvider.data,
      let bytes = CFDataGetBytePtr(data)
    else {
      // 如果无法获取像素数据，保守地假设有透明像素
      return true
    }

    let bytesPerPixel = cgImage.bitsPerPixel / 8
    let bytesPerRow = cgImage.bytesPerRow

    // 采样检查像素的Alpha值
    for y in stride(from: 0, to: height, by: sampleStep) {
      for x in stride(from: 0, to: width, by: sampleStep) {
        let pixelOffset = y * bytesPerRow + x * bytesPerPixel

        // 根据Alpha信息确定Alpha字节的位置
        var alphaIndex: Int
        switch cgImage.alphaInfo {
        case .premultipliedLast, .last:
          alphaIndex = pixelOffset + bytesPerPixel - 1
        case .premultipliedFirst, .first:
          alphaIndex = pixelOffset
        default:
          continue
        }

        // 检查数组边界
        if alphaIndex < CFDataGetLength(data) {
          let alphaValue = bytes[alphaIndex]
          // 如果发现非完全不透明的像素（Alpha < 255），说明有透明度
          if alphaValue < 255 {
            return true
          }
        }
      }
    }

    // 所有采样点都是完全不透明的
    return false
  }

  /// 创建不带Alpha通道的CGImage
  private func createOpaqueImage(from cgImage: CGImage) -> CGImage? {
    let width = cgImage.width
    let height = cgImage.height

    guard let colorSpace = cgImage.colorSpace else { return nil }

    // 创建不带Alpha的context
    guard
      let context = CGContext(
        data: nil,
        width: width,
        height: height,
        bitsPerComponent: 8,
        bytesPerRow: 0,
        space: colorSpace,
        bitmapInfo: CGImageAlphaInfo.noneSkipLast.rawValue
      )
    else { return nil }

    // 设置白色背景
    context.setFillColor(UIColor.white.cgColor)
    context.fill(CGRect(x: 0, y: 0, width: width, height: height))

    // 绘制原图
    context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))

    return context.makeImage()
  }

  @ViewBuilder
  func imageView(_ hideGrids: Bool = false) -> some View {
    GeometryReader {
      let size = $0.size

      if let image {
        Image(uiImage: image)
          .resizable()
          .aspectRatio(contentMode: .fill)
          .overlay(content: {
            GeometryReader { proxy in
              let rect = proxy.frame(in: .named("CROPVIEW"))

              Color.clear
                .onChange(of: isInteracting) { _, newValue in
                  withAnimation(.easeInOut(duration: 0.3)) {
                    if rect.minX > 0 {
                      offset.width -= rect.minX
                      HapticFeedbackManager.shared.trigger(.impactMedium)
                    }
                    if rect.minY > 0 {
                      offset.height -= rect.minY
                      HapticFeedbackManager.shared.trigger(.impactMedium)
                    }
                    if rect.maxX < size.width {
                      offset.width = rect.minX - offset.width
                      HapticFeedbackManager.shared.trigger(.impactMedium)
                    }
                    if rect.maxY < size.height {
                      offset.height = rect.minY - offset.height
                      HapticFeedbackManager.shared.trigger(.impactMedium)
                    }
                  }
                  if !newValue {
                    lastOffset = offset
                  }
                }
            }
          })
          .frame(width: size.width, height: size.height)
      }
    }
    .scaleEffect(scale)
    .offset(offset)
    .overlay(content: {
      if !hideGrids {
        Grids()
      }
    })
    .coordinateSpace(name: "CROPVIEW")
    .gesture(
      DragGesture()
        .updating(
          $isInteracting,
          body: { _, state, _ in
            state = true
          }
        )
        .onChanged({ value in
          offset = CGSize(
            width: value.translation.width + lastOffset.width,
            height: value.translation.height + lastOffset.height)
        })
    )
    .gesture(
      MagnificationGesture()
        .updating(
          $isInteracting,
          body: { _, state, _ in
            state = true
          }
        )
        .onChanged({ value in
          let updatedScale = lastScale + value
          scale = updatedScale < 1 ? 1 : updatedScale
        })
        .onEnded({ value in
          withAnimation(.easeInOut(duration: 0.3)) {
            if scale < 1 {
              scale = 1
              lastScale = 0
            } else {
              lastScale = scale - 1
            }
          }
        })
    )
    .frame(width: cropSize, height: cropSize)
    .cornerRadius(0)
  }

  @ViewBuilder
  func Grids() -> some View {
    // 标准九宫格：2条竖线 + 2条横线 + 四周边框
    GeometryReader { geometry in
      let size = geometry.size

      ZStack {
        // 四周边框
        // 上边框
        Rectangle()
          .fill(.white.opacity(0.7))
          .frame(width: size.width, height: 1)
          .position(x: size.width / 2, y: 0)

        // 下边框
        Rectangle()
          .fill(.white.opacity(0.7))
          .frame(width: size.width, height: 1)
          .position(x: size.width / 2, y: size.height)

        // 左边框
        Rectangle()
          .fill(.white.opacity(0.7))
          .frame(width: 1, height: size.height)
          .position(x: 0, y: size.height / 2)

        // 右边框
        Rectangle()
          .fill(.white.opacity(0.7))
          .frame(width: 1, height: size.height)
          .position(x: size.width, y: size.height / 2)

        // 竖线 - 位于 1/3 和 2/3 处
        ForEach(1...2, id: \.self) { index in
          Rectangle()
            .fill(.white.opacity(0.7))
            .frame(width: 1, height: size.height)
            .position(x: size.width * CGFloat(index) / 3, y: size.height / 2)
        }

        // 横线 - 位于 1/3 和 2/3 处
        ForEach(1...2, id: \.self) { index in
          Rectangle()
            .fill(.white.opacity(0.7))
            .frame(width: size.width, height: 1)
            .position(x: size.width / 2, y: size.height * CGFloat(index) / 3)
        }
      }
    }
  }
}

@available(iOS 16.0, *)
struct CustomImagePicker_Previews: PreviewProvider {
  static var previews: some View {
    CropView(image: UIImage(named: "Pic")) { _, _ in
      // Preview placeholder
    }
  }
}
