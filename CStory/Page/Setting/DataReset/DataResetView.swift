//
//  DataResetView.swift
//  CStory
//
//  Created by AI Assistant on 2025/8/11.
//

import Foundation
import SwiftData
import SwiftUI

/// 数据重置页面
///
/// 提供数据重置功能，包括清除所有交易记录、卡片信息等。
/// 为了防止误操作，提供了确认机制和详细的说明。
struct DataResetView: View {

  // MARK: - Dependencies

  @Environment(\.dataManager) private var dataManager: DataManagement
  @Environment(\.modelContext) private var modelContext
  @EnvironmentObject private var pathManager: PathManagerHelper

  // MARK: - ViewModel

  @State private var viewModel: DataResetVM?

  // MARK: - Body

  var body: some View {
    VStack(spacing: 0) {
      // MARK: 导航栏
      NavigationBarKit(
        viewModel: NavigationBarKitVM(
          title: "重新来过",
          backAction: {
            dataManager.hapticManager.trigger(.impactLight)
            pathManager.path.removeLast()
          }
        )
      )

      // MARK: 主要内容
      ScrollView {
        VStack(spacing: 24) {
          // 说明区域
          infoSection

          // 重置按钮区域
          resetButtonSection
        }
        .padding(.horizontal, 16)
        .padding(.top, 24)
        .padding(.bottom, 80)
      }
      .background(.cLightBlue)
    }
    .background(.cLightBlue)
    .navigationBarTitleDisplayMode(.inline)
    .navigationBarBackButtonHidden(true)
    .onAppear {
      // 初始化viewModel
      if viewModel == nil {
        viewModel = DataResetVM(modelContext: modelContext, dataManager: dataManager)
      }
    }
    .alert(
      "确认重置",
      isPresented: Binding(
        get: { viewModel?.showingResetConfirmation ?? false },
        set: { viewModel?.showingResetConfirmation = $0 }
      )
    ) {
      Button("取消", role: .cancel) {}
      Button("确认重置", role: .destructive) {
        viewModel?.performReset()
      }
    } message: {
      Text("此操作将删除所有数据，包括交易记录、卡片信息等，且无法恢复。确定要继续吗？")
    }
    .alert(
      "重置完成",
      isPresented: Binding(
        get: { viewModel?.resetCompleted ?? false },
        set: { viewModel?.resetCompleted = $0 }
      )
    ) {
      Button("确定") {
        // 返回到主页面
        pathManager.path = NavigationPath()
        viewModel?.resetState()
      }
    } message: {
      Text("所有数据已成功清除，应用将返回初始状态。")
    }
    .alert(
      "重置失败",
      isPresented: Binding(
        get: { viewModel?.showingError ?? false },
        set: { viewModel?.showingError = $0 }
      )
    ) {
      Button("确定") {}
    } message: {
      Text(viewModel?.errorMessage ?? "未知错误")
    }
  }

  // MARK: - Subviews

  /// 说明区域
  private var infoSection: some View {
    VStack(spacing: 16) {
      // 标题
      HStack {
        Image(systemName: "exclamationmark.triangle.fill")
          .font(.system(size: 24))
          .foregroundColor(.orange)

        Text("数据重置说明")
          .font(.system(size: 18, weight: .semibold))
          .foregroundColor(.cBlack)

        Spacer()
      }

      // 说明内容
      VStack(alignment: .leading, spacing: 12) {
        infoItem(
          icon: "trash.fill",
          title: "清除所有数据",
          description: "将删除所有交易记录、分类、卡片等数据"
        )

        infoItem(
          icon: "arrow.clockwise",
          title: "恢复初始状态",
          description: "应用将回到首次安装时的状态"
        )

        infoItem(
          icon: "exclamationmark.circle.fill",
          title: "无法恢复",
          description: "此操作不可逆，请确保已备份重要数据"
        )
      }
    }
    .padding(20)
    .background(.cWhite)
    .cornerRadius(16)
    .overlay(
      RoundedRectangle(cornerRadius: 16)
        .strokeBorder(Color.orange.opacity(0.2), lineWidth: 1)
    )
  }

  /// 说明项
  private func infoItem(icon: String, title: String, description: String) -> some View {
    HStack(alignment: .top, spacing: 12) {
      Image(systemName: icon)
        .font(.system(size: 16, weight: .medium))
        .foregroundColor(.orange)
        .frame(width: 20)

      VStack(alignment: .leading, spacing: 4) {
        Text(title)
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.cBlack)

        Text(description)
          .font(.system(size: 12, weight: .regular))
          .foregroundColor(.cBlack.opacity(0.6))
      }

      Spacer()
    }
  }

  /// 重置按钮区域
  private var resetButtonSection: some View {
    VStack(spacing: 16) {
      // 重置按钮
      Button(action: {
        dataManager.hapticManager.trigger(.warning)
        viewModel?.showResetConfirmation()
      }) {
        HStack {
          if viewModel?.isResetting == true {
            ProgressView()
              .scaleEffect(0.8)
              .tint(.white)
          } else {
            Image(systemName: "trash.fill")
              .font(.system(size: 16, weight: .medium))
          }

          Text((viewModel?.isResetting == true) ? "重置中..." : "重置所有数据")
            .font(.system(size: 16, weight: .semibold))
        }
        .foregroundColor(.white)
        .frame(maxWidth: .infinity)
        .frame(height: 50)
        .background(
          LinearGradient(
            colors: [Color.red, Color.red.opacity(0.8)],
            startPoint: .leading,
            endPoint: .trailing
          )
        )
        .cornerRadius(12)
      }
      .disabled(viewModel?.isResetting == true)

      // 提示文字
      Text("请谨慎操作，此操作无法撤销")
        .font(.system(size: 12, weight: .regular))
        .foregroundColor(.cBlack.opacity(0.5))
    }
    .padding(20)
    .background(.cWhite)
    .cornerRadius(16)
    .overlay(
      RoundedRectangle(cornerRadius: 16)
        .strokeBorder(.cAccentBlue.opacity(0.08), lineWidth: 1)
    )
  }
}

// MARK: - Preview

#Preview {
  DataResetView()
    .environment(\.dataManager, DataManagement())
    .environmentObject(PathManagerHelper())
}
