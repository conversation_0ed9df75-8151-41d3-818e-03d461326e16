//
//  CategorySortView.swift
//  CStory
//
//  Created by NZUE on 2025/7/24.
//

import SwiftData
import SwiftUI

/// 类别排序视图
///
/// 通用的类别排序页面，支持主类别和子类别的拖拽排序。
/// 使用MVVM架构，根据传入参数决定显示的内容。
struct CategorySortView: View {
  // MARK: - Properties

  @StateObject var viewModel: CategorySortVM

  /// 环境变量
  @Environment(\.dismiss) private var dismiss
  @Environment(\.dataManager) private var dataManager

  // MARK: - Body

  var body: some View {
    VStack(spacing: 0) {
      // 标题栏
      NavigationBarKit(
        viewModel: NavigationBarKitVM.backOnly(
          title: viewModel.title,
          backAction: {
            dataManager.hapticManager.trigger(.impactLight)
            dismiss()
          }
        )
      )

      // 列表内容
      List {
        switch viewModel.mode {
        case .mainCategory:
          ForEach(viewModel.mainCategories, id: \.id) { category in
            categoryRow(icon: category.icon, name: category.name)
          }
          .onMove { source, destination in
            dataManager.hapticManager.trigger(.impactLight)
            viewModel.moveCategory(from: source, to: destination)
          }

        case .subCategory:
          ForEach(viewModel.subCategories, id: \.id) { category in
            categoryRow(icon: category.icon, name: category.name)
          }
          .onMove { source, destination in
            dataManager.hapticManager.trigger(.impactLight)
            viewModel.moveCategory(from: source, to: destination)
          }
        }
      }
    }
    .background(Color("C_LightBlue"))
    .navigationBarTitleDisplayMode(.inline)
    .navigationBarBackButtonHidden(true)
  }

  // MARK: - Helper Methods

  /// 创建类别行视图
  @ViewBuilder
  private func categoryRow(icon: IconType, name: String) -> some View {
    HStack(spacing: 12) {
      // 图标
      IconView(
        viewModel: IconViewVM(
          icon: icon,
          size: 40,
          fontSize: 20,
          backgroundColor: .cAccentBlue.opacity(0.1),
          cornerRadius: 10
        ))

      // 名称
      Text(name)
        .font(.system(size: 16, weight: .medium))
        .foregroundColor(.cBlack)

      Spacer()

      // 拖拽指示图标
      Image(systemName: "line.3.horizontal")
        .foregroundColor(.cBlack.opacity(0.3))
        .font(.system(size: 14))
    }
    .contentShape(Rectangle())
  }
}
