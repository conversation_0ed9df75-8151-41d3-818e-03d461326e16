//
//  TransactionCategorySettingsVM.swift
//  CStory
//
//  Created by NZUE on 2025/1/23.
//

import SwiftData
import SwiftUI

/// 交易类别设置视图的ViewModel
///
/// 负责管理交易类别设置界面的业务逻辑和状态管理，包括：
/// - 类别类型切换（支出/收入）
/// - 类别筛选和排序
/// - 类别操作（编辑、删除、迁移）
/// - 拖拽排序
/// - 删除确认和迁移逻辑
@MainActor
class TransactionCategoryVM: ObservableObject {

  // MARK: - Published Properties

  /// 当前选择的类别类型
  @Published var selectedType: TransactionType = .expense
  /// 选中的主类别ID
  @Published var selectedMainCategoryId: String? = nil
  /// 是否显示子类别
  @Published var showSubCategories = false
  /// 是否显示操作菜单
  @Published var showingActionSheet = false
  /// 当前选中的要操作的类别ID
  @Published var selectedCategoryId: String? = nil
  /// 当前选中的类别是否是主类别
  @Published var isSelectedMainCategory = true

  // 迁移账单相关状态
  /// 是否显示类别选择器
  @Published var showingCategorySelector = false
  /// 临时交易对象（用于类别选择）
  @Published var tempTransaction: TransactionModel?
  /// 迁移源类别ID
  @Published var migrationSourceCategoryId: String? = nil

  // 添加迁移成功提示相关状态
  /// 是否显示迁移成功提示
  @Published var showMigrationSuccess = false
  /// 迁移成功的数量
  @Published var migratedCount = 0

  // 删除相关状态
  /// 是否显示删除确认对话框
  @Published var showDeleteConfirmation = false
  /// 待删除的类别ID
  @Published var categoryToDelete: String?
  /// 是否显示删除前迁移提示
  @Published var showDeleteMigrationAlert = false
  /// 待删除类别的交易数量
  @Published var deleteAffectedTransactionCount = 0
  /// 是否显示有子类别提示
  @Published var showHasSubCategoriesAlert = false

  // MARK: - Dependencies

  private let hapticManager = HapticFeedbackManager.shared

  /// 数据管理器
  private let dataManager: DataManagement
  /// 数据上下文
  private let modelContext: ModelContext

  // MARK: - Initialization

  /// 初始化ViewModel
  /// - Parameters:
  ///   - dataManager: 数据管理器
  ///   - modelContext: 数据上下文
  init(dataManager: DataManagement, modelContext: ModelContext) {
    self.dataManager = dataManager
    self.modelContext = modelContext
  }

  // MARK: - Computed Properties

  /// 根据类型筛选的类别列表
  var filteredCategories: [TransactionMainCategoryModel] {
    return dataManager.mainCategories.filter { $0.type == selectedType.rawValue }
      .sorted { $0.order < $1.order }
  }

  /// 选中的主类别
  var selectedMainCategory: TransactionMainCategoryModel? {
    return dataManager.mainCategories.first { $0.id == selectedMainCategoryId }
  }

  /// 检查是否存在重复的类别
  var hasDuplicates: Bool {
    var uniqueIds = Set<String>()

    for category in dataManager.mainCategories {
      if !uniqueIds.insert(category.id).inserted {
        return true
      }
    }
    return false
  }

  // MARK: - UI Helper Methods

  /// 检查是否应该显示子分类
  func shouldShowSubCategories(for category: TransactionMainCategoryModel) -> Bool {
    return selectedMainCategoryId == category.id && showSubCategories
      && category.subCategories != nil
  }

  /// 获取排序后的子分类
  func getSortedSubCategories(for category: TransactionMainCategoryModel)
    -> [TransactionSubCategoryModel]
  {
    return category.subCategories?.sorted(by: { $0.order < $1.order }) ?? []
  }

  /// 创建分类行的ViewModel
  func createCategoryRowViewModel(for category: TransactionMainCategoryModel)
    -> CategorySettingRowVM
  {
    let isExpanded = selectedMainCategoryId == category.id && showSubCategories

    return CategorySettingRowVM(
      from: category,
      isExpanded: isExpanded,
      onTap: {
        self.handleMainCategoryTap(categoryId: category.id)
      },
      onMoreAction: {
        self.showMainCategoryActions(categoryId: category.id)
      }
    )
  }

  /// 创建导航栏的ViewModel
  func createNavigationBarViewModel(
    onDismiss: @escaping () -> Void,
    onRemoveDuplicates: @escaping () -> Void
  ) -> NavigationBarKitVM {
    let rightButton: RightButtonConfig? =
      hasDuplicates
      ? .text("去重", action: onRemoveDuplicates)
      : nil

    return NavigationBarKitVM(
      title: "分类管理",
      backAction: onDismiss,
      rightButton: rightButton
    )
  }

  // MARK: - Actions

  /// 切换类别类型
  func switchType(to type: TransactionType) {
    hapticManager.trigger(.selection)
    withAnimation(.spring(response: 0.2, dampingFraction: 0.9)) {
      selectedType = type
      selectedMainCategoryId = nil
      showSubCategories = false
    }
  }

  /// 处理主类别点击
  func handleMainCategoryTap(categoryId: String) {
    hapticManager.trigger(.selection)
    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
      if selectedMainCategoryId == categoryId {
        showSubCategories.toggle()
      } else {
        selectedMainCategoryId = categoryId
        showSubCategories = true
      }
    }
  }

  /// 显示主类别操作菜单
  func showMainCategoryActions(categoryId: String) {
    hapticManager.trigger(.impactLight)
    selectedCategoryId = categoryId
    isSelectedMainCategory = true
    showingActionSheet = true
  }

  /// 显示子类别操作菜单
  func showSubCategoryActions(categoryId: String) {
    hapticManager.trigger(.selection)
    selectedCategoryId = categoryId
    isSelectedMainCategory = false
    showingActionSheet = true
  }

  /// 移除重复的类别数据
  func removeDuplicates() {
    hapticManager.trigger(.impactMedium)

    // 处理主类别去重
    removeDuplicateMainCategories()

    // 处理子类别去重
    removeDuplicateSubCategories()
  }

  /// 移除重复的主类别
  private func removeDuplicateMainCategories() {
    let allMainCategories = dataManager.mainCategories

    // 按类别ID分组，找出重复项
    let groupedCategories = Dictionary(grouping: allMainCategories) { $0.id }
    var totalRemoved = 0

    // 处理每组重复的主类别
    for (categoryId, categories) in groupedCategories {
      guard categories.count > 1 else { continue }

      // 按order排序，保留排序靠前的
      let sortedCategories = categories.sorted { $0.order < $1.order }
      let toRemove = Array(sortedCategories.dropFirst())

      // 删除重复的类别
      for category in toRemove {
        modelContext.delete(category)
        totalRemoved += 1
      }

      print("主类别 \(categoryId): 保留排序靠前的，删除 \(toRemove.count) 个重复项")
    }

    // 保存更改
    do {
      try modelContext.save()
      if totalRemoved > 0 {
        print("✅ 主类别去重完成：删除了 \(totalRemoved) 个重复类别")
      }
    } catch {
      print("❌ 主类别去重失败: \(error.localizedDescription)")
    }
  }

  /// 移除重复的子类别
  private func removeDuplicateSubCategories() {
    let allSubCategories = dataManager.subCategories

    // 按子类别ID分组，找出重复项
    let groupedSubCategories = Dictionary(grouping: allSubCategories) { $0.id }
    var totalRemoved = 0

    // 处理每组重复的子类别
    for (subCategoryId, subCategories) in groupedSubCategories {
      guard subCategories.count > 1 else { continue }

      // 按order排序，保留排序靠前的
      let sortedSubCategories = subCategories.sorted { $0.order < $1.order }
      let toRemove = Array(sortedSubCategories.dropFirst())

      // 删除重复的子类别
      for subCategory in toRemove {
        modelContext.delete(subCategory)
        totalRemoved += 1
      }

      print("子类别 \(subCategoryId): 保留排序靠前的，删除 \(toRemove.count) 个重复项")
    }

    // 保存更改
    do {
      try modelContext.save()
      if totalRemoved > 0 {
        print("✅ 子类别去重完成：删除了 \(totalRemoved) 个重复类别")
      }
    } catch {
      print("❌ 子类别去重失败: \(error.localizedDescription)")
    }
  }

  /// 处理类别排序
  func moveCategory(fromIndex: Int, toIndex: Int) {

    // 获取当前类型的类别
    let categories = filteredCategories
    guard fromIndex >= 0, fromIndex < categories.count,
      toIndex >= 0, toIndex < categories.count
    else { return }

    withAnimation(.spring(response: 0.3)) {
      // 获取需要移动的类别
      let movingCategory = categories[fromIndex]
      let targetOrder = categories[toIndex].order

      // 计算新的顺序
      if fromIndex < toIndex {
        // 向下移动
        for index in (fromIndex + 1)...toIndex {
          categories[index].order -= 1
        }
      } else {
        // 向上移动
        for index in toIndex..<fromIndex {
          categories[index].order += 1
        }
      }

      // 设置移动项的新顺序
      movingCategory.order = targetOrder

      // 保存更改
      try? modelContext.save()
    }
  }

  // MARK: - Migration Methods

  /// 准备迁移账单
  func prepareMigrateTransactions(from categoryId: String) {
    // 保存源类别ID
    migrationSourceCategoryId = categoryId

    // 创建临时交易对象
    let temp = TransactionModel(
      id: UUID(), transactionType: .expense, transactionAmount: 0, currency: "", symbol: "",
      expenseToCardRate: 1.0, expenseToBaseRate: 1.0, incomeToCardRate: 1.0,
      incomeToBaseRate: 1.0, isStatistics: false, remark: "", transactionDate: Date(),
      createdAt: Date(), updatedAt: Date()
    )

    // 设置交易类型与源类别相同
    if let mainCategory = dataManager.mainCategories.first(where: { $0.id == categoryId }) {
      temp.transactionType = TransactionType(rawValue: mainCategory.type) ?? .expense
    } else if let subCategory = dataManager.subCategories.first(where: { $0.id == categoryId }),
      let mainCategory = dataManager.mainCategories.first(where: { $0.id == subCategory.mainId })
    {
      temp.transactionType = TransactionType(rawValue: mainCategory.type) ?? .expense
    } else {
      // 默认为当前选择的类型
      temp.transactionType = selectedType
    }

    // 保存临时交易
    tempTransaction = temp

    // 显示类别选择器
    showingCategorySelector = true
  }

  /// 执行交易迁移
  func migrateTransactions(from fromCategoryId: String, to toCategoryId: String) {

    // 确保源类别ID和目标类别ID不同
    guard fromCategoryId != toCategoryId else { return }

    // 查找使用源类别ID的所有交易
    let affectedTransactions = dataManager.allTransactions.filter {
      $0.transactionCategoryId == fromCategoryId
    }

    // 更新交易的类别ID
    for transaction in affectedTransactions {
      transaction.transactionCategoryId = toCategoryId
    }

    // 保存更改
    try? modelContext.save()

    // 显示成功提示
    let count = affectedTransactions.count
    if count > 0 {
      // 设置迁移成功状态
      migratedCount = count
      withAnimation {
        showMigrationSuccess = true
      }

      // 3秒后自动隐藏
      DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
        withAnimation {
          self.showMigrationSuccess = false
        }
      }
    }
  }

  /// 完成迁移后的清理
  func completeMigration() {
    showingCategorySelector = false
    tempTransaction = nil
    migrationSourceCategoryId = nil
    selectedCategoryId = nil
  }

  /// 取消迁移
  func cancelMigration() {
    showingCategorySelector = false
    tempTransaction = nil
    migrationSourceCategoryId = nil
    selectedCategoryId = nil
  }

  // MARK: - Delete Methods

  /// 检查类别是否有关联的交易
  func checkCategoryTransactions(_ categoryId: String) -> Int {
    return dataManager.allTransactions.filter { $0.transactionCategoryId == categoryId }.count
  }

  /// 检查主类别是否有子类别
  func hasSubCategories(_ categoryId: String) -> Bool {
    if let mainCategory = dataManager.mainCategories.first(where: { $0.id == categoryId }) {
      return !(mainCategory.subCategories?.isEmpty ?? true)
    }
    return false
  }

  /// 准备删除类别
  func prepareDeleteCategory(_ categoryId: String) {
    categoryToDelete = categoryId

    // 检查是否有子类别
    if hasSubCategories(categoryId) {
      // 有子类别，显示提示
      showingActionSheet = false
      showHasSubCategoriesAlert = true
      return
    }

    deleteAffectedTransactionCount = checkCategoryTransactions(categoryId)

    if deleteAffectedTransactionCount > 0 {
      // 有交易，显示迁移提示
      showDeleteMigrationAlert = true
    } else {
      // 没有交易，直接显示确认对话框
      showDeleteConfirmation = true
    }

    showingActionSheet = false
  }

  /// 执行删除类别（包括清除相关交易的类别ID）
  func deleteCategory(_ categoryId: String) {

    // 清除相关交易的类别ID
    let affectedTransactions = dataManager.allTransactions.filter {
      $0.transactionCategoryId == categoryId
    }
    for transaction in affectedTransactions {
      transaction.transactionCategoryId = nil
    }

    // 查找并删除类别
    if let index = dataManager.mainCategories.firstIndex(where: { $0.id == categoryId }) {
      let category = dataManager.mainCategories[index]
      modelContext.delete(category)

      // 保存更改
      try? modelContext.save()

      // 重置状态
      resetDeleteState()
    } else if let index = dataManager.subCategories.firstIndex(where: { $0.id == categoryId }) {
      // 删除子类别
      let subCategory = dataManager.subCategories[index]
      modelContext.delete(subCategory)

      // 保存更改
      try? modelContext.save()

      // 重置状态
      resetDeleteState()
    }
  }

  /// 重置删除相关状态
  func resetDeleteState() {
    categoryToDelete = nil
    deleteAffectedTransactionCount = 0
    showDeleteConfirmation = false
    showDeleteMigrationAlert = false
    showHasSubCategoriesAlert = false
    selectedCategoryId = nil
  }

  /// 取消删除
  func cancelDelete() {
    resetDeleteState()
  }

  // MARK: - Helper Methods

  /// 获取过滤后的类别列表
  func getFilteredCategories(for transaction: TransactionModel) -> [TransactionMainCategoryModel] {
    return dataManager.mainCategories
      .filter { $0.type == transaction.transactionType.rawValue }
      .sorted { $0.order < $1.order }
  }

  /// 获取子类别对应的主类别ID
  func getMainCategoryId(for subCategoryId: String) -> String? {
    return dataManager.subCategories.first { $0.id == subCategoryId }?.mainId
  }

  // MARK: - ActionSheet 处理方法

  /// 处理编辑操作
  func handleEditAction(pathManager: PathManagerHelper) {
    guard let categoryId = selectedCategoryId else { return }

    pathManager.path.append(
      NavigationDestination.editTransactionCategoryView(
        categoryId: categoryId,
        isMainCategory: isSelectedMainCategory
      )
    )
    dismissActionSheet()
  }

  /// 处理排序操作
  func handleSortAction(pathManager: PathManagerHelper) {
    guard let categoryId = selectedCategoryId else { return }

    if isSelectedMainCategory {
      // 主类别排序
      pathManager.path.append(
        NavigationDestination.categorySort(
          mode: .mainCategory(type: selectedType)
        )
      )
    } else {
      // 子类别排序 - 需要找到关联的主类别
      if let mainCategoryId = getMainCategoryId(for: categoryId) {
        pathManager.path.append(
          NavigationDestination.categorySort(
            mode: .subCategory(mainCategoryId: mainCategoryId)
          )
        )
      }
    }
    dismissActionSheet()
  }

  /// 处理迁移操作
  func handleMigrateAction() {
    guard let categoryId = selectedCategoryId else { return }
    showingActionSheet = false  // 先关闭 ActionSheet
    prepareMigrateTransactions(from: categoryId)
    // 不重置 selectedCategoryId，因为迁移过程中还需要用到
  }

  /// 处理删除操作
  func handleDeleteAction() {
    guard let categoryId = selectedCategoryId else { return }
    showingActionSheet = false  // 先关闭 ActionSheet
    prepareDeleteCategory(categoryId)
    // 不重置 selectedCategoryId，因为删除过程中还需要用到
  }

  /// 关闭 ActionSheet
  func dismissActionSheet() {
    showingActionSheet = false
    selectedCategoryId = nil
  }
}
