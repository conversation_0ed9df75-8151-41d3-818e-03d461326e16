import SwiftUI

/// 记账设置页面
///
/// 用户可以在这里配置默认的记账方式、默认卡片等偏好设置
struct TransactionSettingsView: View {

  // MARK: - Properties

  @Environment(\.dataManager) private var dataManager
  @Environment(\.dismiss) private var dismiss
  @EnvironmentObject private var pathManager: PathManagerHelper
  @StateObject private var viewModel: TransactionSettingsVM

  // Sheet状态
  @State private var showExpenseCardSheet = false
  @State private var showIncomeCardSheet = false

  // MARK: - Initialization

  init() {
    self._viewModel = StateObject(wrappedValue: TransactionSettingsVM())
  }

  // MARK: - Body

  var body: some View {
    mainContent
      .floatingSheet(
        isPresented: $showExpenseCardSheet,
        config: SheetBase(
          maxDetent: .fraction(0.6),
          cornerRadius: 24,
          interactiveDimiss: true,
          hPadding: 8,
          bPadding: 4
        )
      ) {
        expenseCardSheet
      }
      .floatingSheet(
        isPresented: $showIncomeCardSheet,
        config: SheetBase(
          maxDetent: .fraction(0.6),
          cornerRadius: 24,
          interactiveDimiss: true,
          hPadding: 8,
          bPadding: 4
        )
      ) {
        incomeCardSheet
      }
  }

  // MARK: - Subviews

  /// 主要内容
  private var mainContent: some View {
    VStack(spacing: 0) {
      // MARK: 导航栏
      NavigationBarKit(
        viewModel: NavigationBarKitVM(
          title: "记账设置",
          backAction: {
            dataManager.hapticManager.trigger(.impactLight)
            pathManager.path.removeLast()
          }
        )
      )

      // MARK: 主要内容
      ScrollView {
        VStack(spacing: 24) {
          settingsSection
        }
        .padding(.horizontal, 16)
        .padding(.top, 12)
        .padding(.bottom, 80)
      }
      .background(.cLightBlue)
    }
    .background(.cLightBlue)
    .navigationBarTitleDisplayMode(.inline)
    .navigationBarBackButtonHidden(true)
    .onAppear {
      viewModel.loadData(dataManager: dataManager)
    }
  }

  /// 设置区域
  private var settingsSection: some View {
    VStack(spacing: 12) {

      HStack {
        Text("默认记账方式")
          .frame(maxWidth: .infinity, alignment: .leading)
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.cBlack)
        // 右侧文本切换按钮
        HStack(spacing: 4) {
          Button(action: {
            dataManager.hapticManager.trigger(.selection)
            viewModel.setRecordingMode(.manual)
          }) {
            Text("手动")
              .font(.system(size: 14, weight: .medium))
              .foregroundColor(viewModel.selectedRecordingMode == .manual ? .cWhite : .cBlack)
              .padding(.horizontal, 12)
              .padding(.vertical, 6)
              .background(
                viewModel.selectedRecordingMode == .manual ? .cAccentBlue : .clear
              )
              .cornerRadius(8)
          }

          Button(action: {
            dataManager.hapticManager.trigger(.selection)
            viewModel.setRecordingMode(.ai)
          }) {
            Text("AI")
              .font(.system(size: 14, weight: .medium))
              .foregroundColor(viewModel.selectedRecordingMode == .ai ? .cWhite : .cBlack)
              .padding(.horizontal, 12)
              .padding(.vertical, 6)
              .background(viewModel.selectedRecordingMode == .ai ? .cAccentBlue : .clear)
              .cornerRadius(8)
          }
        }
        .padding(2)
        .background(.cAccentBlue.opacity(0.1))
        .cornerRadius(10)
      }
      .padding(12)
      .background(.cWhite)
      .cornerRadius(16)
      .overlay(
        RoundedRectangle(cornerRadius: 16)
          .strokeBorder(.cAccentBlue.opacity(0.08), lineWidth: 1)
      )

      // 只在AI记账模式下显示AI输入方式设置
      if viewModel.selectedRecordingMode == .ai {
        aiInputModeRow
      }

      // 只在手动记账模式下显示默认卡片设置
      if viewModel.selectedRecordingMode == .manual {
        // 默认支出卡片
        cardSelectionRow(
          title: "默认支出卡片",
          subtitle: "不设置时自动选择第一张储蓄卡",
          selectedCard: viewModel.defaultExpenseCard,
          icon: "minus.circle.fill",
          onTap: {
            dataManager.hapticManager.trigger(.selection)
            showExpenseCardSheet = true
          }
        )

        // 默认收入卡片
        cardSelectionRow(
          title: "默认收入卡片",
          subtitle: "不设置时自动选择第一张储蓄卡",
          selectedCard: viewModel.defaultIncomeCard,
          icon: "plus.circle.fill",
          onTap: {
            dataManager.hapticManager.trigger(.selection)
            showIncomeCardSheet = true
          }
        )
      }

    }
  }

  /// 支出卡片选择Sheet
  private var expenseCardSheet: some View {
    cardSelectionSheet(
      title: "选择默认支出卡片",
      selectedCard: viewModel.defaultExpenseCard,
      onSelect: { card in
        viewModel.setDefaultExpenseCard(card)
        showExpenseCardSheet = false
      },
      onCancel: {
        showExpenseCardSheet = false
      }
    )
  }

  /// 收入卡片选择Sheet
  private var incomeCardSheet: some View {
    cardSelectionSheet(
      title: "选择默认收入卡片",
      selectedCard: viewModel.defaultIncomeCard,
      onSelect: { card in
        viewModel.setDefaultIncomeCard(card)
        showIncomeCardSheet = false
      },
      onCancel: {
        showIncomeCardSheet = false
      }
    )
  }

  /// AI输入方式行
  private var aiInputModeRow: some View {
    HStack {
      Text("AI输入方式")
        .frame(maxWidth: .infinity, alignment: .leading)
        .font(.system(size: 14, weight: .medium))
        .foregroundColor(.cBlack)

      // 右侧文本切换按钮
      HStack(spacing: 4) {
        Button(action: {
          dataManager.hapticManager.trigger(.selection)
          viewModel.setAIInputMode(.text)
        }) {
          Text("文字")
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(viewModel.selectedAIInputMode == .text ? .cWhite : .cBlack)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
              viewModel.selectedAIInputMode == .text ? .cAccentBlue : .clear
            )
            .cornerRadius(8)
        }

        Button(action: {
          dataManager.hapticManager.trigger(.selection)
          viewModel.setAIInputMode(.voice)
        }) {
          Text("语音")
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(viewModel.selectedAIInputMode == .voice ? .cWhite : .cBlack)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(viewModel.selectedAIInputMode == .voice ? .cAccentBlue : .clear)
            .cornerRadius(8)
        }
      }
      .padding(2)
      .background(.cAccentBlue.opacity(0.1))
      .cornerRadius(10)
    }
    .padding(12)
    .background(.cWhite)
    .cornerRadius(16)
    .overlay(
      RoundedRectangle(cornerRadius: 16)
        .strokeBorder(.cAccentBlue.opacity(0.08), lineWidth: 1)
    )
  }

  /// 卡片选择行
  private func cardSelectionRow(
    title: String,
    subtitle: String,
    selectedCard: CardModel?,
    icon: String,
    onTap: @escaping () -> Void
  ) -> some View {
    Button(action: onTap) {
      HStack {
        Text(title)
          .frame(maxWidth: .infinity, alignment: .leading)
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.cBlack)

        // 右侧显示选中的卡片或未设置状态
        HStack(spacing: 8) {
          if let selectedCard = selectedCard {
            Text(selectedCard.name)
              .font(.system(size: 14, weight: .regular))
              .foregroundColor(.cBlack.opacity(0.6))
          } else {
            Text("未设置")
              .font(.system(size: 14, weight: .regular))
              .foregroundColor(.cBlack.opacity(0.4))
          }

          // 右侧箭头
          Image(systemName: "chevron.right")
            .font(.system(size: 12, weight: .medium))
            .foregroundColor(.cBlack.opacity(0.6))
        }
      }
      .padding(12)
      .background(.cWhite)
      .cornerRadius(16)
      .overlay(
        RoundedRectangle(cornerRadius: 16)
          .strokeBorder(.cAccentBlue.opacity(0.08), lineWidth: 1)
      )
    }
    .buttonStyle(PlainButtonStyle())
  }

  /// 卡片选择Sheet
  private func cardSelectionSheet(
    title: String,
    selectedCard: CardModel?,
    onSelect: @escaping (CardModel?) -> Void,
    onCancel: @escaping () -> Void
  ) -> some View {
    VStack(spacing: 20) {
      // 使用SheetTitle组件
      SheetTitle(
        title: title,
        button: "xmark.circle.fill",
        rightButtonAction: onCancel
      )

      // 不设置选项
      Button(action: {
        dataManager.hapticManager.trigger(.selection)
        onSelect(nil)
      }) {
        HStack {
          Image(systemName: selectedCard == nil ? "checkmark.circle.fill" : "circle")
            .font(.system(size: 20))
            .foregroundColor(selectedCard == nil ? .accentColor : .gray)

          Text("不设置默认卡片")
            .font(.system(size: 16, weight: .medium))
            .foregroundColor(.cBlack)

          Spacer()
        }
        .padding(16)
        .background(
          RoundedRectangle(cornerRadius: 12)
            .fill(.cWhite)
            .shadow(color: .cBlack.opacity(0.05), radius: 2, x: 0, y: 1)
        )
      }
      .buttonStyle(PlainButtonStyle())
      .padding(.horizontal, 16)

      // 卡片列表
      ScrollView {
        LazyVStack(spacing: 12) {
          ForEach(viewModel.availableCards, id: \.id) { card in
            let cardViewModel = CardRowVM(
              from: card,
              isSelected: selectedCard?.id == card.id,
              showTypeTag: true,
              showAdditionalInfo: false
            )
            CardRow(
              viewModel: cardViewModel,
              onTap: {
                dataManager.hapticManager.trigger(.selection)
                onSelect(card)
              }
            )
          }
        }
        .padding(.horizontal, 16)
      }

      Spacer()
    }
  }

}

#Preview {
  TransactionSettingsView()
    .withDataManager(DataManagement())
}
