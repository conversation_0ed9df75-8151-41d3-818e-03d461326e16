//
//  TransactionCardView.swift
//  CStory
//
//  Created by NZUE on 2025/7/31.
//

import SwiftUI

/// 交易卡片显示组件
///
/// 统一的卡片显示组件，用于显示交易相关的卡片信息。
/// 支持编辑模式，可以显示编辑按钮并响应点击事件。
///
/// ## 功能特性
/// - 统一的卡片信息显示
/// - 支持编辑模式切换
/// - 自动适配卡片图标显示
/// - 触觉反馈支持
///
/// ## 使用示例
/// ```swift
/// TransactionCardView(
///   card: cardModel,
///   cardType: "支出卡片",
///   isEditing: true,
///   onEditCard: { /* 编辑逻辑 */ }
/// )
/// ```
struct TransactionCardView: View {
  // MARK: - Properties

  /// 卡片模型数据
  let card: CardModel?

  /// 卡片类型描述
  let cardType: String

  /// 是否处于编辑模式
  let isEditing: Bool

  /// 编辑按钮点击回调
  let onEditCard: (() -> Void)?

  // MARK: - Private Properties

  /// 数据管理器
  @Environment(\.dataManager) private var dataManager

  // MARK: - Initialization

  /// 标准初始化方法
  /// - Parameters:
  ///   - card: 卡片模型数据，为nil时显示占位内容
  ///   - cardType: 卡片类型描述
  ///   - isEditing: 是否处于编辑模式，默认为false
  ///   - onEditCard: 编辑按钮点击回调，默认为nil
  init(
    card: CardModel?,
    cardType: String,
    isEditing: Bool = false,
    onEditCard: (() -> Void)? = nil
  ) {
    self.card = card
    self.cardType = cardType
    self.isEditing = isEditing
    self.onEditCard = onEditCard
  }

  // MARK: - Body

  var body: some View {
    HStack(spacing: 12) {
      // 卡片图标
      cardIcon

      // 卡片信息
      VStack(alignment: .leading, spacing: 4) {
        Text(card?.name ?? "未知卡片")
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.cBlack)

        Text(cardType)
          .font(.system(size: 13, weight: .regular))
          .foregroundColor(.cBlack.opacity(0.6))
      }

      Spacer()

      // 编辑按钮
      if isEditing && onEditCard != nil {
        ActionButton(
          viewModel: ActionButtonVM.customColor(
            title: "编辑卡片",
            action: {
              dataManager.hapticManager.trigger(.selection)
              onEditCard?()
            },
            textColor: .cAccentBlue,
            strokeColor: .cAccentBlue.opacity(0.08)
          )
        )
        .transition(.move(edge: .trailing).combined(with: .opacity))
      }
    }
    .padding(.horizontal, 24)
    .contentShape(Rectangle())
    .onTapGesture {
      if isEditing {
        dataManager.hapticManager.trigger(.selection)
        onEditCard?()
      }
    }
  }

  // MARK: - Private Views

  /// 卡片图标视图
  @ViewBuilder
  private var cardIcon: some View {
    IconView(
      viewModel: IconViewVM.optionalImage(
        card?.bankLogo,
        size: 40,
        style: IconStyle(
          backgroundColor: .cAccentBlue.opacity(0.1),
          cornerRadius: 12
        )
      )
    )
  }
}

// MARK: - Convenience Extensions

extension TransactionCardView {
  /// 便捷初始化方法 - 适用于退款场景
  /// - Parameters:
  ///   - cardId: 卡片ID
  ///   - cards: 所有卡片列表
  ///   - cardType: 卡片类型描述
  ///   - isEditing: 是否处于编辑模式
  ///   - onEditCard: 编辑按钮点击回调
  static func withCardId(
    _ cardId: UUID?,
    from cards: [CardModel],
    cardType: String,
    isEditing: Bool = false,
    onEditCard: (() -> Void)? = nil
  ) -> TransactionCardView {
    let card = cardId.flatMap { id in
      cards.first { $0.id == id }
    }

    return TransactionCardView(
      card: card,
      cardType: cardType,
      isEditing: isEditing,
      onEditCard: onEditCard
    )
  }
}

// MARK: - Preview Provider

#if DEBUG
  struct TransactionCardView_Previews: PreviewProvider {
    static var previews: some View {
      VStack(spacing: 16) {
        // 正常卡片
        TransactionCardView(
          card: createMockCard(name: "招商银行信用卡"),
          cardType: "支出卡片",
          isEditing: false
        )

        // 编辑模式
        TransactionCardView(
          card: createMockCard(name: "支付宝余额"),
          cardType: "收入卡片",
          isEditing: true,
          onEditCard: {
            print("编辑卡片")
          }
        )

        // 未知卡片
        TransactionCardView(
          card: nil,
          cardType: "退款卡片",
          isEditing: true,
          onEditCard: {
            print("选择卡片")
          }
        )
      }
      .padding()
      .background(.cWhite)
      .previewLayout(.sizeThatFits)
      .previewDisplayName("TransactionCardView")
    }

    /// 创建模拟卡片数据
    private static func createMockCard(name: String) -> CardModel {
      CardModel(
        id: UUID(),
        order: 1,
        isCredit: false,
        isSelected: true,
        name: name,
        remark: "",
        currency: "CNY",
        symbol: "¥",
        balance: 1000.0,
        credit: 0.0,
        isStatistics: true,
        cover: "",
        bankLogo: nil,
        bankName: "测试银行",
        cardNumber: "1234",
        billDay: nil,
        isFixedDueDay: true,
        dueDay: nil,
        createdAt: Date(),
        updatedAt: Date()
      )
    }
  }
#endif
