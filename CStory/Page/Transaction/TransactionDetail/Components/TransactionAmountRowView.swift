//
//  TransactionAmountRowView.swift
//  CStory
//
//  Created by NZUE on 2025/7/31.
//

import SwiftUI

/// 交易金额行显示组件
///
/// 统一的金额显示组件，用于显示交易相关的金额信息。
/// 支持编辑模式，可以显示编辑按钮并响应点击事件。
///
/// ## 功能特性
/// - 统一的金额显示格式
/// - 支持编辑模式切换
/// - 自适应的样式（普通/总计）
/// - 触觉反馈支持
///
/// ## 使用示例
/// ```swift
/// TransactionAmountRowView(
///   title: "小计",
///   currencySymbol: "¥",
///   amount: 299.99,
///   isTotal: false,
///   isEditing: true,
///   onEditTap: { /* 编辑逻辑 */ }
/// )
/// ```
struct TransactionAmountRowView: View {
  // MARK: - Properties

  /// 显示标题
  let title: String

  /// 货币符号
  let currencySymbol: String

  /// 金额数值
  let amount: Double

  /// 是否为总计样式（影响字体大小和颜色）
  let isTotal: Bool

  /// 是否处于编辑模式
  let isEditing: Bool

  /// 编辑按钮点击回调
  let onEditTap: (() -> Void)?

  // MARK: - Private Properties

  /// 数据管理器
  @Environment(\.dataManager) private var dataManager

  // MARK: - Initialization

  /// 标准初始化方法
  /// - Parameters:
  ///   - title: 显示标题
  ///   - currencySymbol: 货币符号
  ///   - amount: 金额数值
  ///   - isTotal: 是否为总计样式，默认为false
  ///   - isEditing: 是否处于编辑模式，默认为false
  ///   - onEditTap: 编辑按钮点击回调，默认为nil
  init(
    title: String,
    currencySymbol: String,
    amount: Double,
    isTotal: Bool = false,
    isEditing: Bool = false,
    onEditTap: (() -> Void)? = nil
  ) {
    self.title = title
    self.currencySymbol = currencySymbol
    self.amount = amount
    self.isTotal = isTotal
    self.isEditing = isEditing
    self.onEditTap = onEditTap
  }

  // MARK: - Body

  var body: some View {
    Button(action: {
      if isEditing {
        dataManager.hapticManager.trigger(.selection)
        onEditTap?()
      }
    }) {
      HStack(spacing: 0) {
        // 标题
        Text(title)
          .font(.system(size: 14, weight: .regular))
          .foregroundColor(.cBlack.opacity(0.4))

        Spacer()

        HStack(spacing: 6) {
          // 金额显示
          if isTotal {
            displayCurrencyView(size: .large)
          } else {
            displayCurrencyView(size: .medium)
          }

          // 编辑按钮
          if isEditing && onEditTap != nil {
            Image("edit_icon")
              .font(.system(size: 16))
              .foregroundColor(.cAccentBlue)
              .transition(.move(edge: .trailing).combined(with: .opacity))
          }
        }
      }

    }
    .buttonStyle(PlainButtonStyle())
    .contentShape(Rectangle())
  }

  // MARK: - Private Methods

  /// 根据大小显示货币视图
  /// - Parameter size: 显示大小
  /// - Returns: 货币显示视图
  @ViewBuilder
  private func displayCurrencyView(size: DisplaySize) -> some View {
    switch size {
    case .large:
      DisplayCurrencyView.size18(symbol: currencySymbol, amount: amount)
        .foregroundColor(.cBlack)
        .contentTransition(.numericText(value: amount))
        .animation(.easeInOut(duration: 0.3), value: amount)
    case .medium:
      DisplayCurrencyView.size15(symbol: currencySymbol, amount: amount)
        .foregroundColor(.cBlack)
        .contentTransition(.numericText(value: amount))
        .animation(.easeInOut(duration: 0.3), value: amount)
    }
  }

  // MARK: - Supporting Types

  /// 显示大小枚举
  private enum DisplaySize {
    case medium  // 普通行使用
    case large  // 总计行使用
  }
}

// MARK: - Preview Provider

#if DEBUG
  struct TransactionAmountRowView_Previews: PreviewProvider {
    static var previews: some View {
      VStack(spacing: 16) {
        // 普通金额行
        TransactionAmountRowView(
          title: "小计",
          currencySymbol: "¥",
          amount: 299.99,
          isTotal: false,
          isEditing: false
        )

        // 编辑模式
        TransactionAmountRowView(
          title: "优惠",
          currencySymbol: "¥",
          amount: 20.0,
          isTotal: false,
          isEditing: true,
          onEditTap: {
            print("编辑优惠金额")
          }
        )

        // 总计行
        TransactionAmountRowView(
          title: "合计",
          currencySymbol: "¥",
          amount: 279.99,
          isTotal: true,
          isEditing: false
        )

        // 美元金额
        TransactionAmountRowView(
          title: "Total",
          currencySymbol: "$",
          amount: 89.99,
          isTotal: true,
          isEditing: false
        )
      }
      .padding()
      .background(.cWhite)
      .previewLayout(.sizeThatFits)
      .previewDisplayName("TransactionAmountRowView")
    }
  }
#endif
