//
//  NavigationDestinationHelper.swift
//  CStory
//
//  Created by NZUE on 2025/7/12.
//

import SwiftData
import SwiftUI

// MARK: - Environment Key for Sheet Navigation

/// 设置弹窗导航路径的环境键
/// 用于在设置弹窗内部传递导航路径，解决子页面返回时的路径冲突问题
private struct SettingNavigationPathKey: EnvironmentKey {
  static let defaultValue: Binding<NavigationPath>? = nil
}

extension EnvironmentValues {
  /// 设置弹窗内部的导航路径
  var settingNavigationPath: Binding<NavigationPath>? {
    get { self[SettingNavigationPathKey.self] }
    set { self[SettingNavigationPathKey.self] = newValue }
  }
}

/// 应用中所有可导航的目的地
enum NavigationDestination: Hashable {

  // MARK: - 卡片管理

  /// 卡片分类测试页面
  case cardCategoryView
  /// 卡包页面（使用 DataManagement）
  case cardBagView
  /// 选择银行测试页面
  case selectBankView(isCredit: Bool, mainCategory: CardCategoryResponse)

  // MARK: - 交易相关

  /// 交易记录页面（使用 DataManagement）
  case transactionRecordView
  /// 交易详情页面（使用正式环境的TransactionDetailView）
  case transactionDetailView(UUID)
  /// 交易退款页面（使用正式环境的TransactionRefundView）
  case transactionRefundView(transaction: TransactionModel)

  // MARK: - 分类管理

  /// 交易分类设置页面
  case t_CategoryView
  /// 创建交易分类页面
  case createTransactionCategory(
    isMainCategory: Bool, mainCategoryId: String?, selectedType: TransactionType)
  /// 编辑交易分类页面
  case editTransactionCategoryView(categoryId: String, isMainCategory: Bool)
  /// 类别排序页面
  case categorySort(mode: CategorySortMode)

  // MARK: - 其他功能

  /// 汇率查看页面
  case currencyRateView
  /// 记账设置页面
  case transactionSettingsView
  /// 数据重置页面
  case dataResetView
  /// iCloud同步页面
  case iCloudSyncView

  // MARK: - Hashable 实现

  func hash(into hasher: inout Hasher) {
    switch self {
    case .cardCategoryView:
      hasher.combine(12)
    case .transactionDetailView(let transactionId):
      hasher.combine(1)
      hasher.combine(transactionId)
    case .transactionRefundView(let transaction):
      hasher.combine(2)
      hasher.combine(transaction.id)
    case .cardBagView:
      hasher.combine(10)
    case .selectBankView(let isCredit, let mainCategory):
      hasher.combine(13)
      hasher.combine(isCredit)
      hasher.combine(mainCategory.id)
    case .transactionRecordView:
      hasher.combine(11)

    case .t_CategoryView:
      hasher.combine(6)
    case .createTransactionCategory(let isMainCategory, let mainCategoryId, let selectedType):
      hasher.combine(7)
      hasher.combine(isMainCategory)
      hasher.combine(mainCategoryId)
      hasher.combine(selectedType.rawValue)
    case .editTransactionCategoryView(let categoryId, let isMainCategory):
      hasher.combine(8)
      hasher.combine(categoryId)
      hasher.combine(isMainCategory)
    case .categorySort(let mode):
      hasher.combine(14)
      switch mode {
      case .mainCategory(let type):
        hasher.combine("main")
        hasher.combine(type.rawValue)
      case .subCategory(let mainCategoryId):
        hasher.combine("sub")
        hasher.combine(mainCategoryId)
      }
    case .currencyRateView:
      hasher.combine(9)
    case .transactionSettingsView:
      hasher.combine(15)
    case .dataResetView:
      hasher.combine(16)
    case .iCloudSyncView:
      hasher.combine(17)
    }
  }

  static func == (lhs: NavigationDestination, rhs: NavigationDestination) -> Bool {
    switch (lhs, rhs) {
    case (.cardCategoryView, .cardCategoryView):
      return true
    case (
      .transactionDetailView(let lhsTransactionId), .transactionDetailView(let rhsTransactionId)
    ):
      return lhsTransactionId == rhsTransactionId
    case (.transactionRefundView(let lhsTransaction), .transactionRefundView(let rhsTransaction)):
      return lhsTransaction.id == rhsTransaction.id
    case (.cardBagView, .cardBagView):
      return true
    case (
      .selectBankView(let lhsIsCredit, let lhsMainCategory),
      .selectBankView(let rhsIsCredit, let rhsMainCategory)
    ):
      return lhsIsCredit == rhsIsCredit && lhsMainCategory.id == rhsMainCategory.id
    case (.transactionRecordView, .transactionRecordView):
      return true

    case (.t_CategoryView, .t_CategoryView):
      return true
    case (
      .createTransactionCategory(let lhsIsMainCategory, let lhsMainCategoryId, let lhsSelectedType),
      .createTransactionCategory(let rhsIsMainCategory, let rhsMainCategoryId, let rhsSelectedType)
    ):
      return lhsIsMainCategory == rhsIsMainCategory && lhsMainCategoryId == rhsMainCategoryId
        && lhsSelectedType == rhsSelectedType
    case (
      .editTransactionCategoryView(let lhsCategoryId, let lhsIsMainCategory),
      .editTransactionCategoryView(let rhsCategoryId, let rhsIsMainCategory)
    ):
      return lhsCategoryId == rhsCategoryId && lhsIsMainCategory == rhsIsMainCategory
    case (.categorySort(let lhsMode), .categorySort(let rhsMode)):
      switch (lhsMode, rhsMode) {
      case (.mainCategory(let lhsType), .mainCategory(let rhsType)):
        return lhsType == rhsType
      case (.subCategory(let lhsMainCategoryId), .subCategory(let rhsMainCategoryId)):
        return lhsMainCategoryId == rhsMainCategoryId
      default:
        return false
      }
    case (.currencyRateView, .currencyRateView):
      return true
    case (.transactionSettingsView, .transactionSettingsView):
      return true
    case (.dataResetView, .dataResetView):
      return true
    case (.iCloudSyncView, .iCloudSyncView):
      return true
    default:
      return false
    }
  }
}

// MARK: - View Generation

extension NavigationDestination {
  /// 返回每个导航目的地对应的视图
  /// - Parameter modelContext: SwiftData 模型上下文
  /// - Returns: 对应的 SwiftUI 视图
  @ViewBuilder
  func destinationView(modelContext: ModelContext) -> some View {
    switch self {
    case .cardCategoryView:
      CardCategoryViewWrapper()
    case .transactionDetailView(let transactionId):
      TransactionDetailViewWrapper(transactionId: transactionId)
    case .transactionRefundView(let transaction):
      TransactionRefundViewWrapper(transaction: transaction)
    case .cardBagView:
      CardBagViewWrapper()
    case .selectBankView(let isCredit, let mainCategory):
      SelectBankViewWrapper(isCredit: isCredit, mainCategory: mainCategory)
    case .transactionRecordView:
      TransactionRecordViewWrapper()

    // 分类管理相关页面
    case .t_CategoryView:
      TransactionCategoryViewWrapper()
    case .createTransactionCategory(let isMainCategory, let mainCategoryId, let selectedType):
      CategoryFormViewWrapper(
        mode: .create(
          isMainCategory: isMainCategory, mainCategoryId: mainCategoryId, selectedType: selectedType
        ))
    case .editTransactionCategoryView(let categoryId, let isMainCategory):
      CategoryFormViewWrapper(mode: .edit(categoryId: categoryId, isMainCategory: isMainCategory))
    case .categorySort(let mode):
      CategorySortViewWrapper(mode: mode)

    // 设置相关页面
    case .currencyRateView:
      CurrencyRateViewWrapper()
    case .transactionSettingsView:
      TransactionSettingsViewWrapper()
    case .dataResetView:
      DataResetViewWrapper()
    case .iCloudSyncView:
      iCloudSyncViewWrapper()
    }
  }

  /// 返回设置弹窗内部导航对应的视图
  /// 参考 HomeView 的简洁导航方式，直接复用现有的包装器
  /// - Returns: 对应的 SwiftUI 视图
  @ViewBuilder
  func settingDestinationView() -> some View {
    // 参考 HomeView 的做法，直接使用现有的包装器实现
    // 这样保持了代码的一致性和简洁性
    switch self {
    // 设置相关页面 - 直接使用包装器，它们会自动从环境获取依赖
    case .transactionSettingsView:
      TransactionSettingsViewWrapper()
    case .t_CategoryView:
      TransactionCategoryViewWrapper()
    case .currencyRateView:
      CurrencyRateViewWrapper()
    case .iCloudSyncView:
      iCloudSyncViewWrapper()
    case .dataResetView:
      DataResetViewWrapper()

    // 分类相关页面
    case .createTransactionCategory(let isMainCategory, let mainCategoryId, let selectedType):
      CategoryFormViewWrapper(
        mode: .create(
          isMainCategory: isMainCategory, mainCategoryId: mainCategoryId, selectedType: selectedType
        ))
    case .editTransactionCategoryView(let categoryId, let isMainCategory):
      CategoryFormViewWrapper(mode: .edit(categoryId: categoryId, isMainCategory: isMainCategory))
    case .categorySort(let mode):
      CategorySortViewWrapper(mode: mode)

    // 其他页面不支持在设置弹窗内显示
    default:
      VStack(spacing: 16) {
        Image(systemName: "info.circle")
          .font(.system(size: 48))
          .foregroundColor(.blue)

        Text("页面不支持")
          .font(.title2)
          .fontWeight(.bold)

        Text("此页面不支持在设置弹窗内显示")
          .font(.body)
          .foregroundColor(.secondary)
          .multilineTextAlignment(.center)
      }
      .padding()
    }
  }
}

/// TransactionDetailView 的包装器，用于从 Environment 获取 DataManagement
private struct TransactionDetailViewWrapper: View {
  let transactionId: UUID
  @Environment(\.dataManager) private var dataManager

  var body: some View {
    // 根据transactionId找到对应的TransactionModel
    if let transaction = dataManager.allTransactions.first(where: { $0.id == transactionId }) {
      TransactionDetailView(transaction: transaction, dataManager: dataManager)
    } else {
      // 如果找不到交易，显示错误页面
      VStack(spacing: 20) {
        Image(systemName: "exclamationmark.triangle")
          .font(.system(size: 48))
          .foregroundColor(.orange)

        Text("交易不存在")
          .font(.title2)
          .fontWeight(.bold)

        Text("无法找到指定的交易记录")
          .font(.body)
          .foregroundColor(.secondary)
      }
      .padding()
      .navigationTitle("交易详情")
      .navigationBarTitleDisplayMode(.inline)
    }
  }
}

/// CardBagView 的包装器，用于从 Environment 获取 DataManagement
private struct CardBagViewWrapper: View {
  @Environment(\.dataManager) private var dataManager

  var body: some View {
    CardBagView(dataManager: dataManager)
  }
}

/// TransactionRecordView 的包装器，用于从 Environment 获取 DataManagement
private struct TransactionRecordViewWrapper: View {
  @Environment(\.dataManager) private var dataManager
  @EnvironmentObject private var pathManager: PathManagerHelper

  var body: some View {
    TransactionRecordView(
      viewModel: TransactionRecordVM(
        dataManager: dataManager,
        onTransactionTap: { transaction in
          // 震动反馈 - 统一使用.selection强度
          dataManager.hapticManager.trigger(.selection)
          // 导航到交易详情页面
          pathManager.path.append(NavigationDestination.transactionDetailView(transaction.id))
        }
      )
    )
  }
}

/// TransactionRefundView 的包装器，用于从 Environment 获取 DataManagement
private struct TransactionRefundViewWrapper: View {
  let transaction: TransactionModel
  @Environment(\.dataManager) private var dataManager

  var body: some View {
    TransactionRefundView(transaction: transaction, dataManager: dataManager)
  }
}

/// CategorySortView 的包装器，用于从 Environment 获取 DataManagement
private struct CategorySortViewWrapper: View {
  let mode: CategorySortMode
  @Environment(\.dataManager) private var dataManager
  @Environment(\.modelContext) private var modelContext

  var body: some View {
    CategorySortView(
      viewModel: CategorySortVM(
        mode: mode,
        dataManager: dataManager,
        modelContext: modelContext
      )
    )
  }
}

// MARK: - Wrappers (Environment Injection)

/// CategoryFormView 的包装器，用于从 Environment 获取依赖并构造 VM
struct CategoryFormViewWrapper: View {
  let mode: CategoryFormMode
  @Environment(\.dataManager) private var dataManager
  @Environment(\.modelContext) private var modelContext

  var body: some View {
    CategoryFormView(
      viewModel: CategoryFormVM(mode: mode, dataManager: dataManager, modelContext: modelContext),
      mode: mode
    )
  }
}

/// ManualTransactionView 的包装器，用于从 Environment 获取依赖并构造 VM
struct ManualTransactionViewWrapper: View {
  @Environment(\.dataManager) private var dataManager
  @Binding var showTopContent: Bool
  @Binding var showBottomContent: Bool
  var onTransactionSaved: (() -> Void)? = nil
  var onCloseRequested: (() -> Void)? = nil

  init(
    showTopContent: Binding<Bool>,
    showBottomContent: Binding<Bool>,
    onTransactionSaved: (() -> Void)? = nil,
    onCloseRequested: (() -> Void)? = nil
  ) {
    self._showTopContent = showTopContent
    self._showBottomContent = showBottomContent
    self.onTransactionSaved = onTransactionSaved
    self.onCloseRequested = onCloseRequested
  }

  var body: some View {
    ManualTransactionView(
      viewModel: ManualTransactionVM(dataManager: dataManager),
      showTopContent: $showTopContent,
      showBottomContent: $showBottomContent,
      onTransactionSaved: onTransactionSaved,
      onCloseRequested: onCloseRequested
    )
  }
}

/// AITransactionView 的包装器，用于从 Environment 获取依赖（后续 VM 也将改为构造注入）
struct AITransactionViewWrapper: View {
  @Environment(\.dataManager) private var dataManager
  @Environment(\.modelContext) private var modelContext
  @Binding var showTopContent: Bool
  @Binding var showBottomContent: Bool
  var onSwitchToManual: (() -> Void)?
  var onCloseRequested: (() -> Void)?

  init(
    showTopContent: Binding<Bool>,
    showBottomContent: Binding<Bool>,
    onSwitchToManual: (() -> Void)? = nil,
    onCloseRequested: (() -> Void)? = nil
  ) {
    self._showTopContent = showTopContent
    self._showBottomContent = showBottomContent
    self.onSwitchToManual = onSwitchToManual
    self.onCloseRequested = onCloseRequested
  }

  var body: some View {
    AITransactionView(
      viewModel: AITransactionViewModel(dataManager: dataManager, modelContext: modelContext),
      showTopContent: $showTopContent,
      showBottomContent: $showBottomContent,
      onSwitchToManual: onSwitchToManual,
      onCloseRequested: onCloseRequested
    )
  }
}

/// SettingView 的包装器，用于从 Environment 获取依赖并构造 VM
struct SettingViewWrapper: View {
  @Environment(\.dataManager) private var dataManager

  var body: some View {
    SettingView(viewModel: SettingVM(dataManager: dataManager))
  }
}

/// CardCategoryView 的包装器，用于从 Environment 获取依赖并构造 VM
struct CardCategoryViewWrapper: View {
  @Environment(\.dataManager) private var dataManager

  var body: some View {
    CardCategoryView(viewModel: CardCategoryVM(dataManager: dataManager))
  }
}

/// CurrencyRateView 的包装器，用于从 Environment 获取依赖并构造 VM
struct CurrencyRateViewWrapper: View {
  @Environment(\.dataManager) private var dataManager
  @Environment(\.modelContext) private var modelContext

  var body: some View {
    CurrencyRateView(
      viewModel: CurrencyRateVM(dataManager: dataManager, modelContext: modelContext))
  }
}

/// SelectBankView 的包装器，用于从 Environment 获取依赖并构造 VM
struct SelectBankViewWrapper: View {
  let isCredit: Bool
  let mainCategory: CardCategoryResponse
  @Environment(\.dataManager) private var dataManager

  var body: some View {
    SelectBankView(
      viewModel: SelectBankVM(
        isCredit: isCredit, mainCategory: mainCategory, dataManager: dataManager),
      isCredit: isCredit,
      mainCategory: mainCategory
    )
  }
}

/// TransactionSettingsView 的包装器，用于从 Environment 获取依赖
struct TransactionSettingsViewWrapper: View {
  @Environment(\.dataManager) private var dataManager

  var body: some View {
    TransactionSettingsView()
  }
}

/// DataResetView 的包装器，用于从 Environment 获取依赖
struct DataResetViewWrapper: View {
  @Environment(\.dataManager) private var dataManager
  @Environment(\.settingNavigationPath) private var settingNavigationPath
  @EnvironmentObject private var pathManager: PathManagerHelper

  var body: some View {
    DataResetView()
      .environment(\.settingNavigationPath, settingNavigationPath)
      .environmentObject(
        SettingPathManager(
          settingPath: settingNavigationPath,
          globalPath: pathManager
        ))
  }
}

/// 设置页面专用的路径管理器
/// 用于在设置弹窗内正确处理导航返回操作
class SettingPathManager: ObservableObject {
  private let settingPath: Binding<NavigationPath>?
  private let globalPath: PathManagerHelper

  init(settingPath: Binding<NavigationPath>?, globalPath: PathManagerHelper) {
    self.settingPath = settingPath
    self.globalPath = globalPath
  }

  /// 返回上一页
  func goBack() {
    if let settingPath = settingPath, !settingPath.wrappedValue.isEmpty {
      // 如果在设置弹窗内且有路径，使用弹窗内导航
      settingPath.wrappedValue.removeLast()
    } else {
      // 否则使用全局导航
      if !globalPath.path.isEmpty {
        globalPath.path.removeLast()
      }
    }
  }
}

/// iCloudSyncView 的包装器，用于从 Environment 获取依赖
struct iCloudSyncViewWrapper: View {
  @Environment(\.dataManager) private var dataManager

  var body: some View {
    iCloudSyncView()
  }
}
