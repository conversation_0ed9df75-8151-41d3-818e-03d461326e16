import Combine
import SwiftUI

/// 图标视图模型
///
/// 管理图标组件的数据和业务逻辑，支持emoji和图片两种图标类型。
final class IconViewVM: ObservableObject, Identifiable {

  // MARK: - Published Properties

  /// 图标显示类型
  ///
  /// 支持emoji字符和图片数据两种类型
  @Published var icon: IconType

  /// 图标视图的尺寸大小
  ///
  /// 应用于图标容器的宽度和高度
  @Published var size: CGFloat

  /// 图标字体大小
  ///
  /// 仅对emoji类型有效。如果为nil，则使用size的50%作为字体大小
  @Published var fontSize: CGFloat?

  /// 图标背景颜色
  ///
  /// 图标容器的背景色，支持透明度
  @Published var backgroundColor: Color

  /// 图标容器圆角半径
  ///
  /// 控制图标容器的圆角程度，0为直角，size/2为圆形
  @Published var cornerRadius: CGFloat

  /// 图标选中状态
  ///
  /// 选中时会改变背景色和边框样式
  @Published var isSelected: Bool

  /// 边框线条宽度
  ///
  /// 仅在选中状态或有背景色时显示
  @Published var borderWidth: CGFloat

  /// 动画效果启用状态
  ///
  /// 控制状态切换和属性更改时是否使用动画过渡
  @Published var isAnimationEnabled: Bool

  /// 图标点击事件回调
  ///
  /// 当图标被点击时执行的闭包，可选值
  var onTap: (() -> Void)?

  // MARK: - Computed Properties

  /// 计算实际使用的字体大小
  ///
  /// - Returns: 如果fontSize不为空则返回fontSize，否则返回size的50%
  /// - Complexity: O(1)
  var actualFontSize: CGFloat {
    fontSize ?? (size * 0.5)
  }

  /// 根据选中状态计算背景颜色
  ///
  /// - Returns: 选中时返回主题色透明背景，否则返回设定的背景色
  var selectedBackgroundColor: Color {
    isSelected ? Color.accentColor.opacity(0.2) : backgroundColor
  }

  /// 根据选中状态和背景色计算边框颜色
  ///
  /// - Returns: 选中时返回主题色，有背景色时返回半透明白色，否则透明
  var borderColor: Color {
    if isSelected {
      return Color.accentColor
    } else if backgroundColor != .clear {
      return .cWhite.opacity(0.05)
    } else {
      return .clear
    }
  }

  /// 判断是否应该显示边框
  ///
  /// - Returns: 在选中状态或有背景色时显示边框
  var shouldShowBorder: Bool {
    isSelected || backgroundColor != .clear
  }

  // MARK: - Initialization

  /// 初始化图标视图模型
  ///
  /// 创建一个具有指定配置的图标视图模型实例。
  /// 所有参数都有默认值，可根据需要进行调整。
  ///
  /// - Parameters:
  ///   - icon: 图标类型（emoji或图片）
  ///   - size: 图标容器尺寸，默认44pt
  ///   - fontSize: 字体大小，默认为size的50%
  ///   - backgroundColor: 背景颜色，默认为半透明主题色
  ///   - cornerRadius: 圆角半径，默认12pt
  ///   - isSelected: 选中状态，默认false
  ///   - borderWidth: 边框宽度，默认1pt
  ///   - isAnimationEnabled: 动画启用状态，默认true
  ///   - onTap: 点击回调闭包
  init(
    icon: IconType,
    size: CGFloat = 44,
    fontSize: CGFloat? = nil,
    backgroundColor: Color = Color.accentColor.opacity(0.1),
    cornerRadius: CGFloat = 12,
    isSelected: Bool = false,
    borderWidth: CGFloat = 1,
    isAnimationEnabled: Bool = true,
    onTap: (() -> Void)? = nil
  ) {
    self.icon = icon
    self.size = size
    self.fontSize = fontSize
    self.backgroundColor = backgroundColor
    self.cornerRadius = cornerRadius
    self.isSelected = isSelected
    self.borderWidth = borderWidth
    self.isAnimationEnabled = isAnimationEnabled
    self.onTap = onTap
  }

  // MARK: - Public Methods

  /// 切换图标的选中状态
  ///
  /// 自动在选中和未选中状态间切换。如果启用了动画，切换过程将带有平滑动画效果。
  func toggleSelection() {
    withAnimation(isAnimationEnabled ? .easeInOut(duration: 0.2) : nil) {
      isSelected.toggle()
    }
  }

  /// 设置图标的选中状态
  ///
  /// 指定图标的选中状态。相比toggleSelection()，这个方法提供更精确的状态控制。
  ///
  /// - Parameter selected: 目标选中状态
  func setSelected(_ selected: Bool) {
    withAnimation(isAnimationEnabled ? .easeInOut(duration: 0.2) : nil) {
      isSelected = selected
    }
  }

  /// 更新图标内容
  ///
  /// 替换当前显示的图标。支持在emoji和图片类型间切换。
  /// 如果启用了动画，更换过程将带有渐变效果。
  ///
  /// - Parameter newIcon: 新的图标类型
  func updateIcon(_ newIcon: IconType) {
    withAnimation(isAnimationEnabled ? .easeInOut(duration: 0.3) : nil) {
      icon = newIcon
    }
  }

  /// 处理图标点击事件
  ///
  /// 执行注册的点击回调闭包。如果onTap为空，则不执行任何操作。
  ///
  /// - Note: 该方法通常由IconView调用，不需要手动调用
  func handleTap() {
    onTap?()
  }

  /// 获取动画配置
  ///
  /// 根据动画启用状态返回相应的动画配置。
  /// 用于在视图中保持一致的动画效果。
  ///
  /// - Returns: 启用动画时返回平滑动画，否则返回无动画配置
  func getAnimation() -> Animation {
    isAnimationEnabled ? .easeInOut(duration: 0.2) : .linear(duration: 0)
  }
}

// MARK: - Factory Methods

extension IconViewVM {

  /// 创建emoji类型的图标视图模型
  ///
  /// 快速创建用于显示emoji字符的图标。使用预设样式配置，
  /// 适合大多数常见使用场景。
  ///
  /// - Parameters:
  ///   - emoji: 要显示的emoji字符
  ///   - size: 图标容器大小，默认44pt
  ///   - style: 样式配置，默认为IconStyle.default
  /// - Returns: 配置好的图标视图模型实例
  static func emoji(
    _ emoji: String,
    size: CGFloat = 44,
    style: IconStyle = .default
  ) -> IconViewVM {
    return IconViewVM(
      icon: .emoji(emoji),
      size: size,
      backgroundColor: style.backgroundColor,
      cornerRadius: style.cornerRadius
    )
  }

  /// 创建图片类型的图标视图模型
  ///
  /// 快速创建用于显示图片的图标。支持任何Data格式的图片数据，
  /// 包括PNG、JPEG、HEIF等格式。
  ///
  /// - Parameters:
  ///   - imageData: 图片的二进制数据
  ///   - size: 图标容器大小，默认44pt
  ///   - style: 样式配置，默认为IconStyle.default
  /// - Returns: 配置好的图标视图模型实例
  /// - Note: 如果图片数据无效，将显示占位符
  static func image(
    _ imageData: Data,
    size: CGFloat = 44,
    style: IconStyle = .default
  ) -> IconViewVM {
    return IconViewVM(
      icon: .image(imageData),
      size: size,
      backgroundColor: style.backgroundColor,
      cornerRadius: style.cornerRadius
    )
  }

  /// 创建可选图片类型的图标视图模型
  ///
  /// 当图片数据为空时自动使用question占位符
  ///
  /// - Parameters:
  ///   - imageData: 可选的图片二进制数据
  ///   - size: 图标容器大小，默认44pt
  ///   - style: 样式配置，默认为IconStyle.default
  /// - Returns: 配置好的图标视图模型实例
  static func optionalImage(
    _ imageData: Data?,
    size: CGFloat = 44,
    style: IconStyle = .default
  ) -> IconViewVM {
    let icon: IconType
    if let data = imageData {
      icon = .image(data)
    } else {
      // 使用question图标作为占位符
      if let questionImage = UIImage(named: "question"),
        let questionData = questionImage.pngData()
      {
        icon = .image(questionData)
      } else {
        // 如果question图标也加载失败，使用emoji占位符
        icon = .emoji("❓")
      }
    }

    return IconViewVM(
      icon: icon,
      size: size,
      backgroundColor: style.backgroundColor,
      cornerRadius: style.cornerRadius
    )
  }

  /// 创建可交互的图标视图模型
  ///
  /// 创建支持点击交互和选中状态的图标。
  /// 适用于需要用户选择的场景，如分类选择器、工具栏等。
  ///
  /// - Parameters:
  ///   - icon: 图标类型（emoji或图片）
  ///   - size: 图标容器大小，默认44pt
  ///   - isSelected: 初始选中状态，默认false
  ///   - onTap: 点击时执行的回调
  /// - Returns: 配置好的图标视图模型实例
  static func selectable(
    icon: IconType,
    size: CGFloat = 44,
    isSelected: Bool = false,
    onTap: @escaping () -> Void
  ) -> IconViewVM {
    return IconViewVM(
      icon: icon,
      size: size,
      isSelected: isSelected,
      onTap: onTap
    )
  }
}

// MARK: - Style Configuration

/// 图标样式预设配置
///
/// 提供常用的图标样式组合，包括背景色和圆角配置。
/// 可通过预设样式快速创建具有一致外观的图标。
///
/// ## 可用样式
/// - `.default`: 默认样式，浅蓝色背景，中等圆角
/// - `.circular`: 圆形样式，适合头像类图标
/// - `.square`: 方形样式，无圆角
/// - `.rounded`: 大圆角样式，现代化外观
/// - `.clear`: 透明背景样式，仅显示图标内容
///
/// - SeeAlso: `IconViewVM`
struct IconStyle {
  /// 图标背景颜色
  let backgroundColor: Color
  /// 容器圆角半径
  let cornerRadius: CGFloat

  /// 默认样式 - 浅色主题背景，中等圆角
  static let `default` = IconStyle(
    backgroundColor: Color.accentColor.opacity(0.1),
    cornerRadius: 12
  )

  /// 圆形样式 - 创建完全圆形的图标容器
  static let circular = IconStyle(
    backgroundColor: Color.accentColor.opacity(0.1),
    cornerRadius: 22
  )

  /// 方形样式 - 直角边框，适合简约设计
  static let square = IconStyle(
    backgroundColor: Color.accentColor.opacity(0.1),
    cornerRadius: 0
  )

  /// 大圆角样式 - 现代化外观，较大的圆角半径
  static let rounded = IconStyle(
    backgroundColor: Color.accentColor.opacity(0.1),
    cornerRadius: 16
  )

  /// 透明样式 - 无背景色，仅显示图标本身
  static let clear = IconStyle(
    backgroundColor: .clear,
    cornerRadius: 12
  )
}
