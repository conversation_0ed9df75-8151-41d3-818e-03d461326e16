//
//  ActionButton.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftUI

/// 通用操作按钮组件
///
/// 基于MVVM架构的轻量级操作按钮，适用于应用中的小型交互动作。
/// 通过ActionButtonVM管理按钮的状态、样式和交互逻辑。
///
/// 该组件支持多种样式配置，包括边框样式、颜色主题、状态管理等，
/// 并提供便利初始化器和工厂方法简化使用。
///
/// ## 主要功能
/// - 按钮文本和状态管理
/// - 多种边框样式支持
/// - 点击状态反馈
/// - 启用/禁用状态控制
///
/// ## 使用示例
/// ```swift
/// // 基本使用
/// ActionButton(title: "查看全部") {
///   print("按钮点击")
/// }
///
/// // 虚线样式
/// ActionButton(
///   title: "添加分类",
///   strokeStyle: .dashed
/// ) {
///   // 添加逻辑
/// }
///
/// // 使用ViewModel
/// let viewModel = ActionButtonVM(title: "保存") { ... }
/// ActionButton(viewModel: viewModel)
/// ```
///
/// - Author: NZUE
/// - Version: 2.0 (MVVM架构)
/// - Since: 2025.7.22
struct ActionButton: View {
  @ObservedObject private var viewModel: ActionButtonVM

  /// 使用ViewModel初始化
  init(viewModel: ActionButtonVM) {
    self.viewModel = viewModel
  }

  /// 便利初始化器 - 基本配置
  init(
    title: String,
    action: (() -> Void)? = nil,
    isEnabled: Bool = true,
    textColor: Color = .cBlack.opacity(0.6),
    strokeStyle: BorderStyle = .solid,
    strokeColor: Color = Color.accentColor.opacity(0.08),
    showPressedState: Bool = true
  ) {
    let vm = ActionButtonVM(
      title: title,
      action: action,
      isEnabled: isEnabled,
      textColor: textColor,
      strokeStyle: strokeStyle,
      strokeColor: strokeColor,
      showPressedState: showPressedState
    )
    self.viewModel = vm
  }

  var body: some View {
    let content = Text(viewModel.title)
      .font(viewModel.buttonFont)
      .foregroundColor(viewModel.textColor)
      .padding(.horizontal, viewModel.horizontalPadding)
      .padding(.vertical, viewModel.verticalPadding)
      .overlay(
        RoundedRectangle(cornerRadius: viewModel.cornerRadius)
          .strokeBorder(
            viewModel.strokeColor,
            style: viewModel.strokeStyleConfig
          )
      )

    if viewModel.showPressedState && viewModel.canTap {
      Button(action: viewModel.handleButtonTap) {
        content
      }
      .buttonStyle(PlainButtonStyle())
    } else {
      content
        .onTapGesture {
          viewModel.handleButtonTap()
        }
    }
  }
}

// MARK: - 预览

#Preview("基本使用") {
  VStack(spacing: 20) {
    // 基础按钮 - 便利初始化器
    ActionButton(title: "查看全部") {
      print("按钮点击")
    }

    // 虚线按钮 - 便利初始化器
    ActionButton(
      title: "虚线按钮",
      action: { print("虚线按钮点击") },
      strokeStyle: .dashed
    )

    // 不可点击的标签 - 便利初始化器
    ActionButton(
      title: "标签样式",
      isEnabled: false,
      textColor: .blue
    )

    // 自定义颜色 - 便利初始化器
    ActionButton(
      title: "自定义颜色",
      action: { print("自定义颜色点击") },
      textColor: .red,
      strokeColor: .red
    )
  }
  .padding()
}

#Preview("ViewModel方式") {
  VStack(spacing: 20) {
    // 查看全部按钮
    ActionButton(
      viewModel: ActionButtonVM.viewAllButton(action: {
        print("查看全部点击")
      })
    )

    // 虚线按钮
    ActionButton(
      viewModel: ActionButtonVM.dashedButton(
        title: "添加分类",
        action: {
          print("添加分类点击")
        }
      )
    )

    // 标签样式
    ActionButton(
      viewModel: ActionButtonVM.labelStyle(
        title: "状态标签",
        textColor: .green
      )
    )

    // 自定义颜色
    ActionButton(
      viewModel: ActionButtonVM.customColor(
        title: "重要按钮",
        action: { print("重要按钮点击") },
        textColor: .white,
        strokeColor: .blue
      )
    )

    // 完全使用ViewModel
    ActionButton(
      viewModel: ActionButtonVM(
        title: "高级配置",
        action: { print("高级配置点击") },
        textColor: .purple,
        strokeStyle: .dashed,
        strokeColor: .purple
      )
    )
  }
  .padding()
}

#Preview("样式展示") {
  VStack(spacing: 20) {
    // 大按钮
    ActionButton(
      viewModel: {
        let vm = ActionButtonVM.viewAllButton { print("大按钮") }
        vm.updateTextStyle(fontSize: 16, fontWeight: .medium)
        vm.updatePadding(horizontal: 20, vertical: 10)
        return vm
      }()
    )

    // 小按钮
    ActionButton(
      viewModel: {
        let vm = ActionButtonVM.viewAllButton { print("小按钮") }
        vm.updateTextStyle(fontSize: 11, fontWeight: .light)
        vm.updatePadding(horizontal: 8, vertical: 4)
        return vm
      }()
    )

    // 方角按钮
    ActionButton(
      viewModel: {
        let vm = ActionButtonVM.viewAllButton { print("方角按钮") }
        vm.updateCornerRadius(8)
        return vm
      }()
    )

    // 禁用状态
    ActionButton(
      viewModel: {
        let vm = ActionButtonVM(title: "禁用按钮") { print("不会执行") }
        vm.setEnabled(false)
        return vm
      }()
    )
  }
  .padding()
}
