//
//  FloatingActionButtonVM.swift
//  CStory
//
//  Created by NZUE on 2025/7/22.
//

import SwiftUI

/// 浮动操作按钮配置
struct FloatingActionButton {
  let title: String?
  let iconName: String?
  let action: () -> Void
  let style: ButtonStyle
  let isEnabled: Bool

  /// 文字按钮初始化
  init(
    title: String,
    action: @escaping () -> Void,
    style: ButtonStyle = .primary,
    isEnabled: Bool = true
  ) {
    self.title = title
    self.iconName = nil
    self.action = action
    self.style = style
    self.isEnabled = isEnabled
  }

  /// 图标按钮初始化
  init(
    iconName: String,
    action: @escaping () -> Void,
    style: ButtonStyle = .icon,
    isEnabled: Bool = true
  ) {
    self.title = nil
    self.iconName = iconName
    self.action = action
    self.style = style
    self.isEnabled = isEnabled
  }

  /// 按钮样式枚举
  enum ButtonStyle {
    case primary
    case secondary
    case destructive
    case plain
    case icon

    var backgroundColor: Color {
      switch self {
      case .primary: return Color.accentColor  // 背景使用主色
      case .secondary: return .cBlack.opacity(0.08)
      case .destructive: return .cAccentRed
      case .plain: return .clear
      case .icon: return .cBlack.opacity(0.06)
      }
    }

    var foregroundColor: Color {
      switch self {
      case .primary: return .cWhite  // 文字使用白色，与背景形成对比
      case .secondary: return .cBlack.opacity(0.6)
      case .destructive: return .cWhite  // 红色背景用白色文字
      case .plain: return .cBlack
      case .icon: return .cBlack.opacity(0.4)
      }
    }

    var backgroundOpacity: Double {
      switch self {
      case .primary: return 1.0  // 主按钮使用完全不透明的背景
      case .secondary: return 1.0
      case .destructive: return 1.0  // 危险按钮也用不透明背景
      case .plain: return 0.06
      case .icon: return 1.0
      }
    }

    var disabledBackgroundColor: Color {
      return .cBlack.opacity(0.08)
    }

    var disabledForegroundColor: Color {
      return .cBlack.opacity(0.4)
    }
  }
}

/// 浮动操作按钮视图模型
///
/// 负责浮动操作按钮组件的业务逻辑处理和状态管理，遵循新架构的MVVM模式。
/// 该类管理按钮的状态、样式计算和交互逻辑。
///
/// ## 主要职责
/// - 按钮状态管理（启用/禁用、触觉反馈）
/// - 样式配置（单按钮/多按钮布局）
/// - 交互处理（点击事件、动画）
/// - 显示状态控制（模糊背景、间距等）
///
/// ## 使用示例
/// ```swift
/// let viewModel = FloatingActionButtonVM(
///   buttons: [
///     FloatingActionButton(title: "保存", action: { ... }),
///     FloatingActionButton(title: "取消", action: { ... }, style: .secondary)
///   ]
/// )
/// ```
///
/// - Author: NZUE
/// - Version: 2.0 (新架构)
/// - Since: 2025.7.22
@MainActor
final class FloatingActionButtonVM: ObservableObject {

  // MARK: - Published Properties

  /// 按钮配置数组
  @Published var buttons: [FloatingActionButton]

  /// 按钮间距
  @Published var spacing: CGFloat

  /// 是否使用模糊背景
  @Published var useBlurBackground: Bool

  /// 触觉反馈管理器
  private let hapticManager = HapticFeedbackManager.shared

  // MARK: - Computed Properties

  /// 是否为单按钮模式
  var isSingleButton: Bool {
    buttons.count == 1
  }

  /// 是否有任何禁用的按钮
  var hasDisabledButtons: Bool {
    buttons.contains { !$0.isEnabled }
  }

  /// 启用的按钮数量
  var enabledButtonCount: Int {
    buttons.filter { $0.isEnabled }.count
  }

  // MARK: - Initialization

  /// 单按钮初始化
  init(
    title: String,
    action: @escaping () -> Void,
    style: FloatingActionButton.ButtonStyle = .primary,
    isEnabled: Bool = true,
    useBlurBackground: Bool = true
  ) {
    self.buttons = [
      FloatingActionButton(title: title, action: action, style: style, isEnabled: isEnabled)
    ]
    self.spacing = 32
    self.useBlurBackground = useBlurBackground
  }

  /// 图标按钮初始化
  init(
    iconName: String,
    action: @escaping () -> Void,
    style: FloatingActionButton.ButtonStyle = .icon,
    isEnabled: Bool = true,
    useBlurBackground: Bool = true
  ) {
    self.buttons = [
      FloatingActionButton(iconName: iconName, action: action, style: style, isEnabled: isEnabled)
    ]
    self.spacing = 32
    self.useBlurBackground = useBlurBackground
  }

  /// 多按钮初始化
  init(
    buttons: [FloatingActionButton],
    spacing: CGFloat = 32,
    useBlurBackground: Bool = true
  ) {
    self.buttons = buttons
    self.spacing = spacing
    self.useBlurBackground = useBlurBackground
  }

  // MARK: - Public Methods

  /// 处理按钮点击
  func handleButtonTap(at index: Int) {
    guard index >= 0 && index < buttons.count else { return }
    let button = buttons[index]

    guard button.isEnabled else { return }

    // 根据按钮样式选择合适的触觉反馈
    switch button.style {
    case .destructive:
      hapticManager.trigger(.warning)
    case .primary:
      hapticManager.trigger(.impactMedium)
    default:
      hapticManager.trigger(.impactLight)
    }

    // 执行按钮动作
    button.action()
  }

  /// 更新按钮启用状态
  func updateButtonEnabled(at index: Int, enabled: Bool) {
    guard index >= 0 && index < buttons.count else { return }

    let currentButton = buttons[index]

    if let title = currentButton.title {
      // 文字按钮
      buttons[index] = FloatingActionButton(
        title: title,
        action: currentButton.action,
        style: currentButton.style,
        isEnabled: enabled
      )
    } else if let iconName = currentButton.iconName {
      // 图标按钮
      buttons[index] = FloatingActionButton(
        iconName: iconName,
        action: currentButton.action,
        style: currentButton.style,
        isEnabled: enabled
      )
    }
  }

  /// 添加按钮
  func addButton(_ button: FloatingActionButton) {
    buttons.append(button)
  }

  /// 移除指定索引的按钮
  func removeButton(at index: Int) {
    guard index >= 0 && index < buttons.count else { return }
    buttons.remove(at: index)
  }

  /// 清空所有按钮
  func clearButtons() {
    buttons.removeAll()
  }

  /// 更新间距
  func updateSpacing(_ newSpacing: CGFloat) {
    spacing = newSpacing
  }

  /// 切换模糊背景
  func toggleBlurBackground() {
    useBlurBackground.toggle()
  }

  // MARK: - Style Helper Methods

  /// 获取按钮背景颜色
  func getButtonBackgroundColor(at index: Int) -> Color {
    guard index >= 0 && index < buttons.count else { return .clear }
    let button = buttons[index]

    if button.isEnabled {
      return button.style.backgroundColor.opacity(button.style.backgroundOpacity)
    } else {
      return button.style.disabledBackgroundColor
    }
  }

  /// 获取按钮前景颜色
  func getButtonForegroundColor(at index: Int) -> Color {
    guard index >= 0 && index < buttons.count else { return .clear }
    let button = buttons[index]

    if button.isEnabled {
      return button.style.foregroundColor
    } else {
      return button.style.disabledForegroundColor
    }
  }

  /// 获取按钮圆角半径
  func getButtonCornerRadius(for style: FloatingActionButton.ButtonStyle) -> CGFloat {
    switch style {
    case .icon:
      return 20
    default:
      return 20
    }
  }

  /// 获取按钮尺寸
  func getButtonSize(for style: FloatingActionButton.ButtonStyle) -> CGSize {
    switch style {
    case .icon:
      return CGSize(width: 40, height: 40)
    default:
      return CGSize(width: -1, height: 40)  // -1 表示自适应宽度
    }
  }
}
