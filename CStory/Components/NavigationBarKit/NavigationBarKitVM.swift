//
//  NavigationBarKitVM.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/22.
//

import Combine
import SwiftUI

/// 导航栏视图模型
///
/// 负责NavigationBarKit的业务逻辑和数据管理，遵循MVVM架构模式。
/// 该类处理导航栏的配置、状态管理和交互逻辑。
///
/// ## 主要职责
/// - 导航栏配置管理
/// - 按钮状态和样式控制
/// - 交互事件处理
/// - 动画和视觉效果管理
///
/// ## 使用示例
/// ```swift
/// let viewModel = NavigationBarKitVM(
///   title: "卡包",
///   backAction: { dismiss() },
///   rightButton: .icon("plus") { print("添加") }
/// )
/// NavigationBarKit(viewModel: viewModel)
/// ```
final class NavigationBarKitVM: ObservableObject {

  // MARK: - Published Properties

  /// 标题文本
  @Published var title: String

  /// 导航栏样式
  @Published var style: NavigationBarStyle

  /// 右侧按钮配置
  @Published var rightButton: RightButtonConfig?

  /// 是否启用动画
  @Published var isAnimationEnabled: Bool

  /// 是否显示返回按钮
  @Published var showBackButton: Bool

  /// 返回按钮样式
  @Published var backButtonStyle: NavigationButtonStyle

  // MARK: - Actions

  /// 返回按钮动作
  let backAction: () -> Void

  // MARK: - Computed Properties

  /// 标题字体
  var titleFont: Font {
    style.titleFont
  }

  /// 标题颜色
  var titleColor: Color {
    style.titleColor
  }

  /// 水平内边距
  var horizontalPadding: CGFloat {
    style.horizontalPadding
  }

  /// 垂直内边距
  var verticalPadding: CGFloat {
    style.verticalPadding
  }

  /// 是否有右侧按钮
  var hasRightButton: Bool {
    rightButton != nil
  }

  // MARK: - Initialization

  /// 初始化导航栏视图模型
  /// - Parameters:
  ///   - title: 标题文本
  ///   - backAction: 返回按钮动作
  ///   - rightButton: 右侧按钮配置
  ///   - style: 导航栏样式
  ///   - showBackButton: 是否显示返回按钮
  ///   - backButtonStyle: 返回按钮样式
  ///   - isAnimationEnabled: 是否启用动画
  init(
    title: String,
    backAction: @escaping () -> Void,
    rightButton: RightButtonConfig? = nil,
    style: NavigationBarStyle = .default,
    showBackButton: Bool = true,
    backButtonStyle: NavigationButtonStyle = .back,
    isAnimationEnabled: Bool = true
  ) {
    self.title = title
    self.backAction = backAction
    self.rightButton = rightButton
    self.style = style
    self.showBackButton = showBackButton
    self.backButtonStyle = backButtonStyle
    self.isAnimationEnabled = isAnimationEnabled
  }

  // MARK: - Public Methods

  /// 更新标题
  /// - Parameter newTitle: 新标题
  func updateTitle(_ newTitle: String) {
    withAnimation(isAnimationEnabled ? .easeInOut(duration: 0.3) : nil) {
      title = newTitle
    }
  }

  /// 更新右侧按钮
  /// - Parameter newButton: 新的右侧按钮配置
  func updateRightButton(_ newButton: RightButtonConfig?) {
    withAnimation(isAnimationEnabled ? .easeInOut(duration: 0.3) : nil) {
      rightButton = newButton
    }
  }

  /// 移除右侧按钮
  func removeRightButton() {
    updateRightButton(nil)
  }

  /// 切换返回按钮显示状态
  func toggleBackButton() {
    withAnimation(isAnimationEnabled ? .easeInOut(duration: 0.3) : nil) {
      showBackButton.toggle()
    }
  }

  /// 获取动画配置
  /// - Returns: SwiftUI Animation
  func getAnimation() -> Animation {
    isAnimationEnabled ? .easeInOut(duration: 0.2) : .linear(duration: 0)
  }
}

// MARK: - Factory Methods

extension NavigationBarKitVM {

  /// 创建只有返回按钮的导航栏
  /// - Parameters:
  ///   - title: 标题
  ///   - backAction: 返回动作
  /// - Returns: 配置好的视图模型
  static func backOnly(
    title: String,
    backAction: @escaping () -> Void
  ) -> NavigationBarKitVM {
    return NavigationBarKitVM(
      title: title,
      backAction: backAction
    )
  }

  /// 创建带主要操作按钮的导航栏
  /// - Parameters:
  ///   - title: 标题
  ///   - backAction: 返回动作
  ///   - actionIcon: 操作图标
  ///   - actionTitle: 操作文本
  ///   - action: 操作动作
  /// - Returns: 配置好的视图模型
  static func withPrimaryAction(
    title: String,
    backAction: @escaping () -> Void,
    actionIcon: String? = nil,
    actionTitle: String? = nil,
    action: @escaping () -> Void
  ) -> NavigationBarKitVM {
    let rightButton: RightButtonConfig
    if let actionTitle = actionTitle {
      rightButton = .text(actionTitle, style: .primary, action: action)
    } else if let actionIcon = actionIcon {
      rightButton = .icon(actionIcon, style: .primary, action: action)
    } else {
      rightButton = .icon("checkmark", style: .primary, action: action)
    }

    return NavigationBarKitVM(
      title: title,
      backAction: backAction,
      rightButton: rightButton
    )
  }

  /// 创建带危险操作按钮的导航栏
  /// - Parameters:
  ///   - title: 标题
  ///   - backAction: 返回动作
  ///   - destructiveIcon: 危险操作图标
  ///   - destructiveAction: 危险操作动作
  /// - Returns: 配置好的视图模型
  static func withDestructiveAction(
    title: String,
    backAction: @escaping () -> Void,
    destructiveIcon: String = "trash",
    destructiveAction: @escaping () -> Void
  ) -> NavigationBarKitVM {
    return NavigationBarKitVM(
      title: title,
      backAction: backAction,
      rightButton: .icon(destructiveIcon, style: .destructive, action: destructiveAction)
    )
  }

  /// 创建编辑模式导航栏
  /// - Parameters:
  ///   - title: 标题
  ///   - backAction: 返回动作
  ///   - saveAction: 保存动作
  /// - Returns: 配置好的视图模型
  static func editMode(
    title: String,
    backAction: @escaping () -> Void,
    saveAction: @escaping () -> Void
  ) -> NavigationBarKitVM {
    return NavigationBarKitVM(
      title: title,
      backAction: backAction,
      rightButton: .text("保存", style: .primary, action: saveAction)
    )
  }

  /// 创建设置页面导航栏
  /// - Parameters:
  ///   - title: 标题
  ///   - backAction: 返回动作
  ///   - settingsAction: 设置动作
  /// - Returns: 配置好的视图模型
  static func settings(
    title: String,
    backAction: @escaping () -> Void,
    settingsAction: @escaping () -> Void
  ) -> NavigationBarKitVM {
    return NavigationBarKitVM(
      title: title,
      backAction: backAction,
      rightButton: .icon("gearshape", action: settingsAction)
    )
  }
}

// MARK: - Style Extensions

extension NavigationBarStyle {

  /// 紧凑样式
  static let compact = NavigationBarStyle(
    titleFont: .system(size: 14, weight: .medium),
    titleColor: .cBlack,
    horizontalPadding: 12,
    verticalPadding: 6
  )

  /// 突出样式
  static let prominent = NavigationBarStyle(
    titleFont: .system(size: 20, weight: .bold),
    titleColor: .cBlack,
    horizontalPadding: 20,
    verticalPadding: 16
  )
}

extension NavigationButtonStyle {

  /// 成功操作按钮样式（绿色）
  static let success = NavigationButtonStyle(
    backgroundColor: .cAccentGreen,
    foregroundColor: .cWhite,
    borderColor: .cAccentGreen,
    borderWidth: 1,
    iconFont: .system(size: 16, weight: .medium),
    textFont: .system(size: 14, weight: .medium)
  )

  /// 警告操作按钮样式（橙色）
  static let warning = NavigationButtonStyle(
    backgroundColor: .cWhite,
    foregroundColor: Color.orange,
    borderColor: Color.orange.opacity(0.08),
    borderWidth: 1,
    iconFont: .system(size: 16, weight: .medium),
    textFont: .system(size: 14, weight: .medium)
  )

  /// 透明按钮样式
  static let clear = NavigationButtonStyle(
    backgroundColor: .clear,
    foregroundColor: .cBlack,
    borderColor: .clear,
    borderWidth: 0,
    iconFont: .system(size: 16, weight: .medium),
    textFont: .system(size: 14, weight: .medium)
  )
}
