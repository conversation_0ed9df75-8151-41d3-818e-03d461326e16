import SwiftUI

/// 通用导航栏组件
///
/// 基于MVVM架构的导航栏组件，支持返回按钮、标题和可选的右侧按钮。
struct NavigationBarKit: View {

  // MARK: - Properties

  @ObservedObject var viewModel: NavigationBarKitVM

  /// 数据管理器
  @Environment(\.dataManager) private var dataManager

  // MARK: - Initialization

  /// 使用ViewModel初始化
  /// - Parameter viewModel: 导航栏视图模型
  init(viewModel: NavigationBarKitVM) {
    self.viewModel = viewModel
  }

  // MARK: - Body

  var body: some View {
    HStack {
      // 返回按钮
      if viewModel.showBackButton {
        NavigationButton(
          icon: "chevron.backward",
          action: {
            dataManager.hapticManager.trigger(.impactLight)
            viewModel.backAction()
          },
          style: viewModel.backButtonStyle
        )
        .transition(.move(edge: .leading).combined(with: .opacity))
      }

      Spacer()

      // 标题
      Text(viewModel.title)
        .font(viewModel.titleFont)
        .foregroundColor(viewModel.titleColor)
        .animation(viewModel.getAnimation(), value: viewModel.title)

      Spacer()

      // 右侧按钮或占位空间
      if let rightButton = viewModel.rightButton {
        NavigationButton(
          icon: rightButton.icon,
          text: rightButton.text,
          action: {
            dataManager.hapticManager.trigger(.impactLight)
            rightButton.action()
          },
          style: rightButton.style
        )
        .transition(.move(edge: .trailing).combined(with: .opacity))
      } else {
        // 占位空间，保持标题居中
        Color.clear
          .frame(width: 46, height: 46)
      }
    }
    .padding(.horizontal, viewModel.horizontalPadding)
    .padding(.vertical, viewModel.verticalPadding)
    .animation(viewModel.getAnimation(), value: viewModel.rightButton?.icon)
    .animation(viewModel.getAnimation(), value: viewModel.rightButton?.text)
  }
}

// MARK: - 导航按钮组件

/// 导航栏按钮组件
struct NavigationButton: View {
  let icon: String?
  let text: String?
  let action: () -> Void
  let style: NavigationButtonStyle

  init(
    icon: String? = nil,
    text: String? = nil,
    action: @escaping () -> Void,
    style: NavigationButtonStyle
  ) {
    self.icon = icon
    self.text = text
    self.action = action
    self.style = style
  }

  var body: some View {
    Button(action: action) {
      Group {
        if let text = text {
          Text(text)
            .font(style.textFont)
            .foregroundColor(style.foregroundColor)
        } else if let icon = icon {
          Image(systemName: icon)
            .font(style.iconFont)
            .foregroundColor(style.foregroundColor)
        }
      }
      .frame(width: 46, height: 46)
      .background(style.backgroundColor)
      .cornerRadius(24)
      .overlay(
        RoundedRectangle(cornerRadius: 24)
          .strokeBorder(style.borderColor, lineWidth: style.borderWidth)
      )
    }
    .buttonStyle(PlainButtonStyle())
  }
}

// MARK: - 配置结构体

/// 右侧按钮配置
struct RightButtonConfig {
  let icon: String?
  let text: String?
  let action: () -> Void
  let style: NavigationButtonStyle

  /// 创建图标按钮
  static func icon(
    _ iconName: String,
    style: NavigationButtonStyle = .default,
    action: @escaping () -> Void
  ) -> RightButtonConfig {
    return RightButtonConfig(
      icon: iconName,
      text: nil,
      action: action,
      style: style
    )
  }

  /// 创建文本按钮
  static func text(
    _ text: String,
    style: NavigationButtonStyle = .default,
    action: @escaping () -> Void
  ) -> RightButtonConfig {
    return RightButtonConfig(
      icon: nil,
      text: text,
      action: action,
      style: style
    )
  }
}

/// 导航栏样式
struct NavigationBarStyle {
  let titleFont: Font
  let titleColor: Color
  let horizontalPadding: CGFloat
  let verticalPadding: CGFloat

  /// 默认样式
  static let `default` = NavigationBarStyle(
    titleFont: .system(size: 15, weight: .medium),
    titleColor: .cBlack,
    horizontalPadding: 16,
    verticalPadding: 8
  )

  /// 大标题样式
  static let large = NavigationBarStyle(
    titleFont: .system(size: 18, weight: .semibold),
    titleColor: .cBlack,
    horizontalPadding: 16,
    verticalPadding: 12
  )
}

/// 导航按钮样式
struct NavigationButtonStyle {
  let backgroundColor: Color
  let foregroundColor: Color
  let borderColor: Color
  let borderWidth: CGFloat
  let iconFont: Font
  let textFont: Font

  /// 默认样式（白色背景）
  static let `default` = NavigationButtonStyle(
    backgroundColor: .cWhite,
    foregroundColor: .cBlack,
    borderColor: .cAccentBlue.opacity(0.08),
    borderWidth: 1,
    iconFont: .system(size: 16, weight: .medium),
    textFont: .system(size: 14, weight: .medium)
  )

  /// 返回按钮样式
  static let back = NavigationButtonStyle(
    backgroundColor: .cWhite,
    foregroundColor: .cBlack,
    borderColor: .cAccentBlue.opacity(0.08),
    borderWidth: 1,
    iconFont: .system(size: 16, weight: .medium),
    textFont: .system(size: 14, weight: .medium)
  )

  /// 主要操作按钮样式（蓝色）
  static let primary = NavigationButtonStyle(
    backgroundColor: .cAccentBlue,
    foregroundColor: .cWhite,
    borderColor: .cAccentBlue,
    borderWidth: 1,
    iconFont: .system(size: 16, weight: .medium),
    textFont: .system(size: 14, weight: .medium)
  )

  /// 危险操作按钮样式（红色）
  static let destructive = NavigationButtonStyle(
    backgroundColor: .cWhite,
    foregroundColor: .cAccentRed,
    borderColor: .cAccentRed.opacity(0.08),
    borderWidth: 1,
    iconFont: .system(size: 16, weight: .medium),
    textFont: .system(size: 14, weight: .medium)
  )
}

// MARK: - 预览

#Preview {
  ScrollView {
    VStack(spacing: 30) {

      // MARK: 基础导航栏
      VStack(alignment: .leading, spacing: 15) {
        Text("基础导航栏")
          .font(.title2)
          .fontWeight(.bold)

        VStack(spacing: 10) {
          NavigationBarKit(
            viewModel: NavigationBarKitVM.backOnly(
              title: "基本导航栏",
              backAction: {
                print("返回")
              }
            )
          )

          NavigationBarKit(
            viewModel: NavigationBarKitVM.backOnly(
              title: "使用ViewModel",
              backAction: {
                print("返回")
              }
            )
          )
        }
      }

      Divider()

      // MARK: 带操作按钮
      VStack(alignment: .leading, spacing: 15) {
        Text("带操作按钮")
          .font(.title2)
          .fontWeight(.bold)

        VStack(spacing: 10) {
          NavigationBarKit(
            viewModel: NavigationBarKitVM(
              title: "卡包",
              backAction: { print("返回") },
              rightButton: .icon("plus", action: { print("添加") })
            ))

          NavigationBarKit(
            viewModel: .withPrimaryAction(
              title: "交易记录",
              backAction: { print("返回") },
              actionIcon: "plus",
              action: { print("新建交易") }
            ))

          NavigationBarKit(
            viewModel: .withDestructiveAction(
              title: "交易详情",
              backAction: { print("返回") },
              destructiveAction: { print("删除交易") }
            ))
        }
      }

      Divider()

      // MARK: 文本按钮
      VStack(alignment: .leading, spacing: 15) {
        Text("文本按钮")
          .font(.title2)
          .fontWeight(.bold)

        VStack(spacing: 10) {
          NavigationBarKit(
            viewModel: NavigationBarKitVM(
              title: "编辑分类",
              backAction: { print("返回") },
              rightButton: .text("保存", style: .primary, action: { print("保存") })
            ))

          NavigationBarKit(
            viewModel: .editMode(
              title: "编辑交易",
              backAction: { print("取消") },
              saveAction: { print("保存") }
            ))

          NavigationBarKit(
            viewModel: NavigationBarKitVM(
              title: "交易类别",
              backAction: { print("返回") },
              rightButton: .text("去重", action: { print("去重") })
            ))
        }
      }

      Divider()

      // MARK: 不同样式
      VStack(alignment: .leading, spacing: 15) {
        Text("不同样式")
          .font(.title2)
          .fontWeight(.bold)

        VStack(spacing: 10) {
          NavigationBarKit(
            viewModel: NavigationBarKitVM(
              title: "紧凑样式",
              backAction: { print("返回") },
              rightButton: .icon("gearshape", action: { print("设置") }),
              style: .compact
            ))

          NavigationBarKit(
            viewModel: NavigationBarKitVM(
              title: "大标题样式",
              backAction: { print("返回") },
              rightButton: .icon("plus", style: .primary, action: { print("添加") }),
              style: .large
            ))

          NavigationBarKit(
            viewModel: NavigationBarKitVM(
              title: "突出样式",
              backAction: { print("返回") },
              rightButton: .text("完成", style: .success, action: { print("完成") }),
              style: .prominent
            ))
        }
      }

      Divider()

      // MARK: 动态内容演示
      VStack(alignment: .leading, spacing: 15) {
        Text("动态内容演示")
          .font(.title2)
          .fontWeight(.bold)

        DynamicNavigationDemo()
      }
    }
    .padding()
  }
  .background(Color(.systemGroupedBackground))
}

// MARK: - 动态演示组件

private struct DynamicNavigationDemo: View {
  @State private var currentMode: Mode = .view
  @State private var title: String = "查看模式"

  enum Mode: CaseIterable {
    case view, edit, settings

    var title: String {
      switch self {
      case .view: return "查看模式"
      case .edit: return "编辑模式"
      case .settings: return "设置模式"
      }
    }
  }

  var body: some View {
    VStack(spacing: 15) {
      // 动态导航栏
      NavigationBarKit(viewModel: createViewModel())

      // 模式切换按钮
      HStack(spacing: 10) {
        ForEach(Mode.allCases, id: \.self) { mode in
          Button(mode.title) {
            withAnimation(.spring()) {
              currentMode = mode
              title = mode.title
            }
          }
          .padding(.horizontal, 12)
          .padding(.vertical, 6)
          .background(currentMode == mode ? Color.blue : Color.gray.opacity(0.2))
          .foregroundColor(currentMode == mode ? .white : .primary)
          .cornerRadius(8)
        }
      }
    }
  }

  private func createViewModel() -> NavigationBarKitVM {
    switch currentMode {
    case .view:
      return .withPrimaryAction(
        title: title,
        backAction: { print("返回") },
        actionIcon: "square.and.pencil",
        action: {
          withAnimation { currentMode = .edit }
        }
      )
    case .edit:
      return .editMode(
        title: title,
        backAction: {
          withAnimation { currentMode = .view }
        },
        saveAction: { print("保存") }
      )
    case .settings:
      return .settings(
        title: title,
        backAction: {
          withAnimation { currentMode = .view }
        },
        settingsAction: { print("设置") }
      )
    }
  }
}
