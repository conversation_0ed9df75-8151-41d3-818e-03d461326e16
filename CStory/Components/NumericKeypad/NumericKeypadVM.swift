//
//  NumericKeypadVM.swift
//  CStory
//
//  Created by <PERSON> on 2025/7/22.
//

import SwiftUI

/// 数字键盘显示样式
///
/// 定义数字键盘的显示模式和交互行为。
/// 不同样式适用于不同的使用场景和功能需求。
///
/// ## 样式特点
/// - **standard**: 简单数字输入，内置显示器，单一数据绑定
/// - **transaction**: 交易专用，外部显示器，支持多重数据绑定和复杂操作
enum NumericKeypadStyle {
  /// 标准样式
  ///
  /// 内置显示器的通用数字输入键盘。适用于简单的数值输入场景，
  /// 如设置金额上限、输入数量等单一数值录入需求。
  case standard

  /// 交易样式
  ///
  /// 专为交易创建优化的键盘样式。支持外部显示器、
  /// 多数据绑定、优惠计算等复杂交易录入功能。
  case transaction
}

/// 数字键盘按钮类型
///
/// 定义键盘上所有可用按钮的类型和行为。
/// 每种按钮都有对应的显示文本、字体样式和背景颜色配置。
///
/// ## 按钮分类
/// - **数字按钮**: `number(String)` - 0-9数字输入
/// - **操作按钮**: `add`, `subtract` - 数学运算
/// - **功能按钮**: `decimal`, `delete` - 小数点和删除
/// - **动作按钮**: `save`, `record` - 保存和录音功能
enum NumericKeypadButton: Hashable {
  /// 数字按钮
  ///
  /// 表示0-9的数字输入按钮。
  /// - Parameter String: 数字字符串，如"0", "1", "2"等
  case number(String)

  /// 删除按钮
  ///
  /// 删除最后一个输入字符，支持逐字符回退功能。
  case delete

  /// 加法按钮
  ///
  /// 数学加法运算符，支持表达式计算。
  case add

  /// 减法按钮
  ///
  /// 数学减法运算符，支持表达式计算和负数输入。
  case subtract

  /// 小数点按钮
  ///
  /// 小数点输入，支持精度控制和重复输入验证。
  case decimal

  /// 保存按钮
  ///
  /// 确认输入并执行保存操作的动作按钮。
  case save

  /// 再记按钮
  ///
  /// 快速录入功能按钮，通常触发录音或语音识别。
  case record

  /// 按钮显示文本
  ///
  /// 获取按钮在界面上显示的文本内容。
  /// - Returns: 本地化的按钮文本
  var text: String {
    switch self {
    case .number(let num): return num
    case .delete: return "删除"
    case .add: return "+"
    case .subtract: return "-"
    case .decimal: return "."
    case .save: return "保存"
    case .record: return "再记"
    }
  }

  var font: Font {
    switch self {
    case .delete, .record, .save:
      return .system(size: 18, weight: .medium)
    case .add, .subtract:
      return .system(size: 24, weight: .medium)
    default:
      return .system(size: 24, weight: .medium)
    }
  }

  func backgroundColor(for style: NumericKeypadStyle) -> Color {
    switch self {
    case .delete:
      return style == .standard ? .cAccentRed.opacity(0.1) : Color.red.opacity(0.1)
    case .add, .subtract:
      return style == .standard ? .cAccentBlue.opacity(0.1) : Color.green.opacity(0.1)
    case .record, .save:
      return Color.accentColor.opacity(0.1)
    default:
      return style == .standard ? .cWhite : .cWhite.opacity(0.6)
    }
  }
}

/// 数字键盘配置
struct NumericKeypadConfiguration {
  /// 是否允许负数
  let allowNegative: Bool
  /// 最大小数位数
  let maxDecimalPlaces: Int
  /// 是否显示内置显示器
  let showBuiltinDisplay: Bool
  /// 是否添加容器内边距
  let addContainerPadding: Bool
  /// VStack 间距
  let verticalSpacing: CGFloat
  /// 按钮布局
  let buttonLayout: [[NumericKeypadButton]]

  /// 标准配置
  static func standard(
    allowNegative: Bool = false,
    maxDecimalPlaces: Int = 2
  ) -> NumericKeypadConfiguration {
    return NumericKeypadConfiguration(
      allowNegative: allowNegative,
      maxDecimalPlaces: maxDecimalPlaces,
      showBuiltinDisplay: true,
      addContainerPadding: true,
      verticalSpacing: 8,
      buttonLayout: [
        [.number("1"), .number("2"), .number("3"), .delete],
        [.number("4"), .number("5"), .number("6"), .add],
        [.number("7"), .number("8"), .number("9"), .subtract],
        [.number("0"), .decimal, .save],
      ]
    )
  }

  /// 交易配置
  static func transaction(
    allowNegative: Bool = false,
    maxDecimalPlaces: Int = 2
  ) -> NumericKeypadConfiguration {
    return NumericKeypadConfiguration(
      allowNegative: allowNegative,
      maxDecimalPlaces: maxDecimalPlaces,
      showBuiltinDisplay: false,
      addContainerPadding: false,  // 交易模式不添加容器边距
      verticalSpacing: 4,  // 更紧凑的垂直间距
      buttonLayout: [
        [.number("1"), .number("2"), .number("3"), .delete],
        [.number("4"), .number("5"), .number("6"), .add],
        [.number("7"), .number("8"), .number("9"), .subtract],
        [.record, .number("0"), .decimal, .save],
      ]
    )
  }
}

/// 数字键盘视图模型
///
/// 统一管理数字键盘的状态、样式和交互逻辑，支持标准和交易两种模式。
/// 采用MVVM架构，使用正确的SwiftUI数据流模式。
@MainActor
final class NumericKeypadVM: ObservableObject {

  // MARK: - Configuration

  /// 键盘样式
  let style: NumericKeypadStyle

  /// 键盘配置
  let configuration: NumericKeypadConfiguration

  // MARK: - Published Properties

  /// 按钮文本更新触发器
  @Published var buttonTextUpdateTrigger = false

  // MARK: - Private Properties

  /// 触觉反馈管理器
  private let hapticManager = HapticFeedbackManager.shared

  // MARK: - Bindings (直接操作外部绑定，不存储内部状态)

  /// 外部绑定（标准模式）
  private var textBinding: Binding<String>?

  /// 外部绑定（交易模式）
  private var expressionBinding: Binding<String>?
  private var transactionAmountBinding: Binding<String>?
  private var discountAmountBinding: Binding<String>?

  // MARK: - Private Properties

  /// 运算符集合
  private let operators: Set<Character> = ["+", "-"]

  /// 保存回调
  private var onSave: (() -> Void)?

  /// 再记回调（仅交易模式）
  private var onRecord: (() -> Void)?

  // MARK: - Computed Properties (只读取，不修改状态)

  /// 当前操作的文本
  var currentText: String {
    get {
      let value: String
      switch style {
      case .standard:
        value = textBinding?.wrappedValue ?? "0"
      case .transaction:
        value = expressionBinding?.wrappedValue ?? "0"
      }
      return value
    }
    set {
      switch style {
      case .standard:
        textBinding?.wrappedValue = newValue
      case .transaction:
        expressionBinding?.wrappedValue = newValue
      }
      // 触发按钮文本更新
      buttonTextUpdateTrigger.toggle()
    }
  }

  /// 交易金额
  var transactionAmount: String {
    get { transactionAmountBinding?.wrappedValue ?? "0" }
    set { transactionAmountBinding?.wrappedValue = newValue }
  }

  /// 折扣金额
  var discountAmount: String {
    get { discountAmountBinding?.wrappedValue ?? "0" }
    set { discountAmountBinding?.wrappedValue = newValue }
  }

  /// 按钮布局
  var buttonLayout: [[NumericKeypadButton]] {
    configuration.buttonLayout
  }

  /// 是否显示内置显示器
  var showBuiltinDisplay: Bool {
    configuration.showBuiltinDisplay
  }

  // MARK: - Initialization

  /// 标准模式初始化
  init(
    text: Binding<String>,
    style: NumericKeypadStyle = .standard,
    configuration: NumericKeypadConfiguration? = nil,
    onSave: (() -> Void)? = nil
  ) {
    self.style = style
    self.configuration = configuration ?? NumericKeypadConfiguration.standard()
    self.onSave = onSave

    // 直接存储绑定引用，实现真正的双向绑定
    self.textBinding = text
  }

  /// 交易模式初始化
  init(
    expression: Binding<String>,
    transactionAmount: Binding<String>,
    discountAmount: Binding<String>,
    configuration: NumericKeypadConfiguration? = nil,
    onSave: (() -> Void)? = nil,
    onRecord: (() -> Void)? = nil
  ) {
    self.style = .transaction
    self.configuration = configuration ?? NumericKeypadConfiguration.transaction()
    self.onSave = onSave
    self.onRecord = onRecord

    // 直接存储绑定引用，实现真正的双向绑定
    self.expressionBinding = expression
    self.transactionAmountBinding = transactionAmount
    self.discountAmountBinding = discountAmount
  }

  // MARK: - Public Methods

  /// 处理按钮点击
  func handleButtonTap(_ button: NumericKeypadButton) {
    // 触发触觉反馈
    hapticManager.trigger(.impactLight)

    switch button {
    case .delete:
      deleteLastCharacter()
    case .add, .subtract:
      handleOperator(button.text)
    case .decimal:
      handleDecimalPoint()
    case .record:
      handleRecord()
    case .save:
      handleSave()
    case .number(let num):
      handleNumber(num)
    }
  }

  /// 更新绑定引用（解决动态绑定切换问题）
  func updateBindings(
    expression: Binding<String>,
    transactionAmount: Binding<String>,
    discountAmount: Binding<String>
  ) {
    if style == .transaction {
      self.expressionBinding = expression
      self.transactionAmountBinding = transactionAmount
      self.discountAmountBinding = discountAmount
      // 触发按钮文本更新，确保绑定切换后按钮文本正确显示
      buttonTextUpdateTrigger.toggle()
    }
  }

  /// 长按删除清空
  func handleLongPressDelete() {
    // 长按操作使用中等强度反馈
    hapticManager.trigger(.impactMedium)
    clearText()
  }

  /// 获取动态保存按钮文本
  func dynamicSaveButtonText() -> String {
    // 强制获取最新的文本值
    let text: String
    switch style {
    case .standard:
      text = (textBinding?.wrappedValue ?? "0").trimmingCharacters(in: .whitespaces)
    case .transaction:
      text = (expressionBinding?.wrappedValue ?? "0").trimmingCharacters(in: .whitespaces)
    }

    // 使用ExpressionCalculator检查是否包含运算符
    let hasOperator = ExpressionCalculator.containsOperator(text)
    return hasOperator ? "=" : "保存"
  }

  // MARK: - Private Methods

  private func handleNumber(_ number: String) {
    if currentText == "0" {
      currentText = number
    } else if canAppendDigit() {
      currentText.append(number)
    }
  }

  private func handleOperator(_ op: String) {
    // 如果最后一个字符是小数点，先移除它
    if currentText.last == "." {
      currentText.removeLast()
    }

    // 负数处理
    if op == "-" && configuration.allowNegative {
      if currentText == "0" || currentText.isEmpty {
        currentText = "-"
        return
      }
    }

    if let lastChar = currentText.last, operators.contains(lastChar) {
      // 替换最后的运算符
      currentText.removeLast()
      currentText.append(op)
    } else if currentText.contains(where: { operators.contains($0) }) {
      // 计算当前表达式，然后添加新运算符
      if let result = calculateResult() {
        currentText = formatResult(result) + op
      }
    } else {
      // 直接添加运算符
      currentText.append(op)
    }
  }

  private func handleDecimalPoint() {
    let parts = currentText.split { operators.contains($0) }
    if let lastPart = parts.last, !String(lastPart).contains(".") {
      if operators.contains(currentText.last ?? Character("")) {
        currentText.append("0.")
      } else {
        currentText.append(".")
      }
    }
  }

  private func handleRecord() {
    if style == .transaction {
      let buttonText = dynamicSaveButtonText()
      if buttonText == "保存" {
        processAllAmounts()
        onRecord?()
      } else {
        currentText = processExpression(currentText)
      }
    }
  }

  private func handleSave() {
    let buttonText = dynamicSaveButtonText()
    if buttonText == "保存" {
      if style == .transaction {
        processAllAmounts()
      }
      onSave?()
    } else {
      if let result = calculateResult() {
        currentText = formatResult(result)
      }
    }
  }

  private func processAllAmounts() {
    transactionAmount = processExpression(transactionAmount)
    discountAmount = processExpression(discountAmount)
    // 不需要额外同步，因为直接操作绑定
  }

  private func processExpression(_ expr: String) -> String {
    return ExpressionCalculator.preprocess(expr)
  }

  private func calculateResult() -> Double? {
    return ExpressionCalculator.evaluate(currentText)
  }

  // 已迁移到ExpressionCalculator，此方法保留用于结果格式化

  private func formatResult(_ number: Double) -> String {
    return ExpressionCalculator.formatResult(
      number, maxDecimalPlaces: configuration.maxDecimalPlaces)
  }

  private func canAppendDigit() -> Bool {
    // 如果最后一个字符是运算符，允许输入
    if let lastChar = currentText.last, operators.contains(lastChar) {
      return true
    }

    // 检查小数位数
    let parts = currentText.split { operators.contains($0) }
    if let lastPart = parts.last,
      let dotIndex = lastPart.firstIndex(of: ".")
    {
      let decimals = lastPart[lastPart.index(after: dotIndex)...]
      return decimals.count < configuration.maxDecimalPlaces
    }

    return true
  }

  private func deleteLastCharacter() {
    if !currentText.isEmpty {
      currentText.removeLast()
      // 如果删除后为空或只剩负号，重置为"0"
      if currentText.isEmpty || currentText == "-" {
        currentText = "0"
      }
    }
  }

  private func clearText() {
    currentText = "0"
  }
}

// MARK: - Factory Methods

extension NumericKeypadVM {
  /// 创建标准数字键盘ViewModel
  static func standard(
    text: Binding<String>,
    allowNegative: Bool = false,
    maxDecimalPlaces: Int = 2,
    onSave: (() -> Void)? = nil
  ) -> NumericKeypadVM {
    let config = NumericKeypadConfiguration.standard(
      allowNegative: allowNegative,
      maxDecimalPlaces: maxDecimalPlaces
    )
    return NumericKeypadVM(
      text: text,
      style: .standard,
      configuration: config,
      onSave: onSave
    )
  }

  /// 创建交易数字键盘ViewModel
  static func transaction(
    expression: Binding<String>,
    transactionAmount: Binding<String>,
    discountAmount: Binding<String>,
    allowNegative: Bool = false,
    maxDecimalPlaces: Int = 2,
    onSave: (() -> Void)? = nil,
    onRecord: (() -> Void)? = nil
  ) -> NumericKeypadVM {
    let config = NumericKeypadConfiguration.transaction(
      allowNegative: allowNegative,
      maxDecimalPlaces: maxDecimalPlaces
    )
    return NumericKeypadVM(
      expression: expression,
      transactionAmount: transactionAmount,
      discountAmount: discountAmount,
      configuration: config,
      onSave: onSave,
      onRecord: onRecord
    )
  }
}
