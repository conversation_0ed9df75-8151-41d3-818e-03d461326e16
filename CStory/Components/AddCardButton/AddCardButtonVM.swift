//
//  AddCardButtonVM.swift
//  CStory
//
//  Created by <PERSON> on 2025/7/22.
//

import SwiftUI

/// 添加卡片按钮的视图模型
///
/// 管理添加卡片按钮的业务逻辑、状态和样式配置。
/// 支持多种按钮样式，包括标准样式和最小化样式。
final class AddCardButtonVM: ObservableObject {

  // MARK: - 公共属性

  /// 按钮样式
  let style: AddCardButtonStyle
  /// 导航动作
  let action: () -> Void
  /// 是否在点击时自动关闭当前 sheet
  let shouldDismiss: Bool

  // MARK: - 初始化

  /// 初始化添加卡片按钮视图模型
  /// - Parameters:
  ///   - style: 按钮样式
  ///   - action: 点击动作
  ///   - shouldDismiss: 是否在点击时自动关闭当前 sheet
  init(style: AddCardButtonStyle, action: @escaping () -> Void, shouldDismiss: Bool) {
    self.style = style
    self.action = action
    self.shouldDismiss = shouldDismiss
  }

}

// MARK: - 添加卡片按钮样式

/// 添加卡片按钮样式枚举
enum AddCardButtonStyle {
  /// 标准样式：显示图标和文本，适用于列表中
  case standard
  /// 最小化样式：仅显示加号图标，适用于卡片网格中
  case minimal

  // MARK: - 样式配置

  /// 按钮宽度
  var width: CGFloat? {
    switch self {
    case .standard:
      return nil  // 使用 maxWidth: .infinity
    case .minimal:
      return 100
    }
  }

  /// 按钮高度
  var height: CGFloat {
    switch self {
    case .standard:
      return 48  // 与其他按钮保持一致
    case .minimal:
      return 60  // 与卡片高度保持一致
    }
  }

  /// 圆角半径
  var cornerRadius: CGFloat {
    switch self {
    case .standard:
      return 16
    case .minimal:
      return 16
    }
  }

  /// 背景颜色
  var backgroundColor: Color {
    switch self {
    case .standard:
      return .cWhite
    case .minimal:
      return .clear
    }
  }

  /// 前景色
  var foregroundColor: Color {
    return .cBlack.opacity(0.4)
  }

  /// 边框样式
  var strokeStyle: StrokeStyle {
    return StrokeStyle(lineWidth: 1, dash: [4])
  }

  /// 是否显示图标
  var showIcon: Bool {
    return true
  }

  /// 图标名称
  var iconName: String {
    return "cardPlus_icon"
  }

  /// 图标大小
  var iconSize: CGFloat {
    switch self {
    case .standard:
      return 21
    case .minimal:
      return 20
    }
  }

  /// 是否显示文本
  var showText: Bool {
    switch self {
    case .standard:
      return true
    case .minimal:
      return false
    }
  }

  /// 按钮文本
  var text: String {
    return "添加卡片"
  }

  /// 字体大小
  var fontSize: CGFloat {
    return 15
  }

  /// 字体权重
  var fontWeight: Font.Weight {
    return .medium
  }

  /// 内容间距
  var spacing: CGFloat {
    return 8
  }

  /// 垂直内边距
  var verticalPadding: CGFloat {
    switch self {
    case .standard:
      return 12
    case .minimal:
      return 0
    }
  }
}
