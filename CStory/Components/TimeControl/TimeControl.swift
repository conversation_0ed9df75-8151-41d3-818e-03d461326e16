//
//  TimeControl.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/18.
//

import SwiftUI

// MARK: - 时间选择组件

/// 时间控制样式枚举
enum TimeControlStyle {
  /// 默认样式：垂直布局，周期选择器在上方
  case `default`
  /// 内联样式：水平布局，点击日期切换周期
  case inline
}

/// 时间控制组件
///
/// 这是一个纯粹的UI组件，它接收一个`TimeControlVM`对象并将其数据显示出来。
/// 所有的业务逻辑、数据格式化均由VM处理。
///
/// ## 使用示例
/// ```swift
/// // 方式1: 使用预先准备好的ViewModel（推荐）
/// let viewModel = TimeControlVM(
///     selectedPeriod: .month,
///     currentDate: Date(),
///     onDateChange: { date, period in
///         // 处理时间变化
///     }
/// )
/// TimeControl(viewModel: viewModel, style: .default)
///
/// // 方式2: 直接传入数据参数
/// TimeControl(
///     initialPeriod: .month,
///     initialDate: Date(),
///     style: .inline,
///     onDateChange: { date, period in
///         // 处理时间变化
///     }
/// )
/// ```
struct TimeControl: View {

  // MARK: - Properties

  /// 时间控制视图模型
  @ObservedObject var viewModel: TimeControlVM

  /// 显示样式
  let style: TimeControlStyle

  @Namespace private var animation

  /// 数据管理器
  @Environment(\.dataManager) private var dataManager

  // MARK: - 初始化

  /// 使用预先准备好的ViewModel初始化（推荐使用）
  init(viewModel: TimeControlVM, style: TimeControlStyle = .default) {
    self.viewModel = viewModel
    self.style = style
  }

  /// 直接使用数据参数初始化
  init(
    initialPeriod: TransactionTimePeriod = .month,
    initialDate: Date = Date(),
    style: TimeControlStyle = .default,
    onDateChange: @escaping (Date, TransactionTimePeriod) -> Void
  ) {
    self.viewModel = TimeControlVM(
      selectedPeriod: initialPeriod,
      currentDate: initialDate,
      onDateChange: onDateChange
    )
    self.style = style
  }

  var body: some View {
    switch style {
    case .default:
      defaultStyleView
    case .inline:
      inlineStyleView
    }
  }

  // MARK: - Style Views

  /// 默认样式视图
  private var defaultStyleView: some View {
    VStack(spacing: 8) {
      HStack(spacing: 8) {
        TimePeriodSelector(
          selectedPeriod: $viewModel.selectedPeriod,
          animation: animation
        )

        DateNavigationButtons {
          viewModel.moveDate(forward: $0)
        }
      }

      UnifiedDateDisplay(
        selectedPeriod: viewModel.selectedPeriod,
        currentDate: viewModel.currentDate,
        style: .default,
        resetAction: viewModel.resetToCurrentDate
      )
    }
  }

  /// 内联样式视图
  private var inlineStyleView: some View {
    HStack {
      // 回到当前日期按钮
      Button(action: {
        dataManager.hapticManager.trigger(.impactLight)
        withAnimation(.easeInOut(duration: 0.3)) {
          viewModel.resetToCurrentDate()
        }
      }) {
        Image("reply_icon")
          .font(.system(size: 16))
          .frame(width: 24, height: 24, alignment: .center)
          .foregroundColor(
            viewModel.shouldShowBackToTodayButton
              ? .cAccentBlue
              : .cBlack.opacity(0.6)
          )
      }

      // 时间选择器
      HStack(spacing: 12) {
        Button(action: {
          dataManager.hapticManager.trigger(.impactLight)
          withAnimation(.easeInOut(duration: 0.3)) {
            viewModel.moveDate(forward: false)
          }
        }) {
          Image(systemName: "chevron.left")
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(.cBlack)
        }

        Button(action: {
          dataManager.hapticManager.trigger(.selection)
          withAnimation(.easeInOut(duration: 0.3)) {
            viewModel.toggleTimePeriod()
          }
        }) {
          UnifiedDateDisplay(
            selectedPeriod: viewModel.selectedPeriod,
            currentDate: viewModel.currentDate,
            style: .inline
          )
        }

        Button(action: {
          dataManager.hapticManager.trigger(.impactLight)
          withAnimation(.easeInOut(duration: 0.3)) {
            viewModel.moveDate(forward: true)
          }
        }) {
          Image(systemName: "chevron.right")
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(.cBlack)
        }
      }
      .padding(.horizontal, 12)
      .padding(.vertical, 8)
      .background(.cWhite)
      .cornerRadius(24)
      .overlay(
        RoundedRectangle(cornerRadius: 24)
          .strokeBorder(.cAccentBlue.opacity(0.08), lineWidth: 1)
      )
    }
  }
}

// MARK: - 统一日期显示组件

enum DateDisplayStyle {
  case `default`  // 大字体，带返回按钮
  case inline  // 小字体，无返回按钮
}

/// 统一的日期显示组件
/// 支持两种显示样式，具有优秀的动画效果
private struct UnifiedDateDisplay: View {
  let selectedPeriod: TransactionTimePeriod
  let currentDate: Date
  let style: DateDisplayStyle
  let resetAction: (() -> Void)?  // 可选的重置回调
  @Environment(\.dataManager) private var dataManager

  init(
    selectedPeriod: TransactionTimePeriod,
    currentDate: Date,
    style: DateDisplayStyle,
    resetAction: (() -> Void)? = nil
  ) {
    self.selectedPeriod = selectedPeriod
    self.currentDate = currentDate
    self.style = style
    self.resetAction = resetAction
  }

  // 为默认样式提供返回按钮功能
  var shouldShowBackButton: Bool {
    let calendar = Calendar.current
    let today = Date()

    switch selectedPeriod {
    case .week:
      let currentWeek = calendar.dateComponents(
        [.yearForWeekOfYear, .weekOfYear], from: currentDate)
      let todayWeek = calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: today)
      return currentWeek.yearForWeekOfYear != todayWeek.yearForWeekOfYear
        || currentWeek.weekOfYear != todayWeek.weekOfYear

    case .month:
      let currentMonth = calendar.dateComponents([.year, .month], from: currentDate)
      let todayMonth = calendar.dateComponents([.year, .month], from: today)
      return currentMonth.year != todayMonth.year || currentMonth.month != todayMonth.month

    case .year:
      let currentYear = calendar.component(.year, from: currentDate)
      let todayYear = calendar.component(.year, from: today)
      return currentYear != todayYear
    }
  }

  var body: some View {
    switch style {
    case .default:
      defaultStyleView
    case .inline:
      inlineStyleView
    }
  }

  @ViewBuilder
  private var defaultStyleView: some View {
    HStack(spacing: 4) {
      Button(action: {
        if shouldShowBackButton {
          dataManager.hapticManager.trigger(.impactLight)
          withAnimation(.easeInOut(duration: 0.3)) {
            resetAction?()
          }
        }
      }) {
        Image("reply_icon")
          .font(.system(size: 16))
          .frame(width: 24, height: 24, alignment: .center)
          .foregroundColor(
            shouldShowBackButton ? .cAccentBlue : .cBlack.opacity(0.6)
          )
      }
      .allowsHitTesting(shouldShowBackButton)
      .animation(.easeInOut(duration: 0.4), value: shouldShowBackButton)

      dateContent(fontSize: 14, fontColor: .cBlack.opacity(0.6))

      Spacer()
    }
    .frame(height: 24)
  }

  @ViewBuilder
  private var inlineStyleView: some View {
    dateContent(fontSize: 14, fontColor: .cBlack)
  }

  @ViewBuilder
  private func dateContent(fontSize: CGFloat, fontColor: Color) -> some View {
    HStack(spacing: 0) {
      let calendar = Calendar.current
      let year = calendar.component(.year, from: currentDate)

      // 年份部分 - 始终显示，直接使用格式化字符串
      Text(formatYear(year))
        .font(.system(size: fontSize, weight: .medium))
        .foregroundColor(fontColor)
        .id("year-\(year)")
        .contentTransition(.numericText(value: Double(year)))

      // 月份部分 - 在月视图和周视图时显示
      if selectedPeriod != .year {
        let month = calendar.component(.month, from: currentDate)
        Text("\(month)月")
          .font(.system(size: fontSize, weight: .medium))
          .foregroundColor(fontColor)
          .id("month-\(month)")
          .contentTransition(.numericText(value: Double(month)))
          .transition(
            .asymmetric(
              insertion: .move(edge: .trailing).combined(with: .opacity),
              removal: .move(edge: .trailing).combined(with: .opacity)
            ))
      }

      // 周次部分 - 只在周视图时显示
      if selectedPeriod == .week {
        let weekOfMonth = calendar.component(.weekOfMonth, from: currentDate)
        Text("第\(weekOfMonth)周")
          .font(.system(size: fontSize, weight: .medium))
          .foregroundColor(fontColor)
          .id("week-\(weekOfMonth)")
          .contentTransition(.numericText(value: Double(weekOfMonth)))
          .transition(
            .asymmetric(
              insertion: .move(edge: .trailing).combined(with: .opacity),
              removal: .move(edge: .trailing).combined(with: .opacity)
            ))
      }
    }
    .animation(.easeInOut(duration: 0.3), value: selectedPeriod)
  }

  /// 格式化年份，确保不带千分位分隔符
  private func formatYear(_ year: Int) -> String {
    return "\(year)年"
  }
}

// MARK: - Private Components

/// 时间周期选择器
private struct TimePeriodSelector: View {
  @Binding var selectedPeriod: TransactionTimePeriod
  let animation: Namespace.ID
  @Environment(\.dataManager) private var dataManager

  var body: some View {
    HStack(spacing: 8) {
      ForEach(TransactionTimePeriod.allCases, id: \.self) { period in
        Button(action: {
          dataManager.hapticManager.trigger(.selection)
          withAnimation(.easeInOut(duration: 0.3)) {
            selectedPeriod = period
          }
        }) {
          Text(period.rawValue)
            .font(.system(size: 16, weight: .medium))
            .foregroundColor(
              selectedPeriod == period ? Color.cWhite : Color.cBlack.opacity(0.6)
            )
            .frame(maxWidth: .infinity)
            .frame(height: 34)
            .background(
              ZStack {
                if selectedPeriod == period {
                  Color.cAccentBlue
                    .cornerRadius(14)
                    .overlay(
                      RoundedRectangle(cornerRadius: 14)
                        .strokeBorder(.cAccentBlue.opacity(0.2), lineWidth: 1)
                    )
                    .matchedGeometryEffect(id: "background", in: animation)
                }
              }
            )
        }
      }
    }
    .padding(.horizontal, 2)
    .padding(.vertical, 2)
    .background(.cWhite)
    .cornerRadius(16)
    .overlay(
      RoundedRectangle(cornerRadius: 16)
        .strokeBorder(.cAccentBlue.opacity(0.08), lineWidth: 1)
    )
  }
}

/// 日期导航按钮
private struct DateNavigationButtons: View {
  let moveDate: (Bool) -> Void
  @Environment(\.dataManager) private var dataManager

  var body: some View {
    HStack(spacing: 8) {
      Button(action: {
        dataManager.hapticManager.trigger(.impactLight)
        withAnimation(.easeInOut(duration: 0.3)) {
          moveDate(false)
        }
      }) {
        Image(systemName: "chevron.left")
          .foregroundColor(.cBlack)
          .frame(width: 30, height: 30)
      }

      Button(action: {
        dataManager.hapticManager.trigger(.impactLight)
        withAnimation(.easeInOut(duration: 0.3)) {
          moveDate(true)
        }
      }) {
        Image(systemName: "chevron.right")
          .foregroundColor(.cBlack)
          .frame(width: 30, height: 30)
      }
    }
    .padding(.horizontal, 8)
    .padding(.vertical, 4)
    .background(.cWhite)
    .cornerRadius(16)
    .overlay(
      RoundedRectangle(cornerRadius: 16)
        .strokeBorder(.cAccentBlue.opacity(0.08), lineWidth: 1)
    )
  }
}

// MARK: - 预览代码 (Preview Provider)

#if DEBUG
  struct TimeControl_Previews: PreviewProvider {
    static var previews: some View {
      VStack(spacing: 30) {
        Text("TimeControl 预览").font(.largeTitle).bold()

        VStack(spacing: 16) {
          Text("默认样式").font(.headline)
          TimeControl(
            initialPeriod: .month,
            initialDate: Date(),
            style: .default
          ) { date, period in
            print("默认样式时间选择变更: \(date), \(period)")
          }
        }
        .padding(.horizontal, 16)

        VStack(spacing: 16) {
          Text("内联样式").font(.headline)
          HStack {
            Text("统计")
              .font(.system(size: 15, weight: .medium))
              .foregroundColor(.cBlack)
            Spacer()

            TimeControl(
              initialPeriod: .month,
              initialDate: Date(),
              style: .inline
            ) { date, period in
              print("内联样式时间选择变更: \(date), \(period)")
            }
          }
        }
        .padding(.horizontal, 16)

        Spacer()
      }
      .background(.cLightBlue)
    }
  }
#endif
