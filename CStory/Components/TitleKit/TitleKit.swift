//
//  TitleKit.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftUI

/// 通用标题组件
///
/// 采用MVVM架构的标题组件，支持左侧标题和可选的右侧操作按钮。
/// 通过ViewModel管理标题状态、样式和交互逻辑。
///
/// ## 使用示例
/// ```swift
/// // 基本标题 - ViewModel方式
/// TitleKit(viewModel: TitleKitViewModel(title: "最近交易"))
///
/// // 带按钮的标题 - ViewModel方式
/// TitleKit(viewModel: TitleKitViewModel.withViewAllButton(title: "最近交易") {
///     // 点击事件
/// })
///
/// // 便利初始化器
/// TitleKit(title: "最近交易")
/// ```
///
/// - Author: NZUE
/// - Version: 2.0 (MVVM架构)
/// - Since: 2025.7.22
struct TitleKit: View {
  @ObservedObject private var viewModel: TitleKitVM

  /// 使用ViewModel初始化
  init(viewModel: TitleKitVM) {
    self.viewModel = viewModel
  }

  /// 便利初始化器 - 基本配置
  init(
    title: String,
    rightButtonTitle: String? = nil,
    rightButtonAction: (() -> Void)? = nil
  ) {
    let vm = TitleKitVM(
      title: title,
      rightButtonTitle: rightButtonTitle,
      rightButtonAction: rightButtonAction
    )
    self.viewModel = vm
  }

  var body: some View {
    HStack {
      // 左侧标题
      Text(viewModel.title)
        .font(viewModel.titleFont)
        .foregroundColor(viewModel.titleColor)

      Spacer()

      // 右侧按钮（如果存在）
      if viewModel.hasRightButton,
        let buttonTitle = viewModel.rightButtonTitle
      {
        ActionButton(
          title: buttonTitle,
          action: viewModel.handleRightButtonTap,
          isEnabled: viewModel.isRightButtonEnabled
        )
      }
    }
    // 使用ViewModel中的内边距配置
    .padding(.horizontal, viewModel.horizontalPadding)
    .padding(.top, viewModel.topPadding)
    .padding(.bottom, viewModel.bottomPadding)
  }
}

// MARK: - 预览

#Preview("基本使用") {
  VStack(spacing: 20) {
    // 带查看全部按钮的标题 - ViewModel方式
    TitleKit(
      viewModel: TitleKitVM.withViewAllButton(
        title: "最近交易",
        action: {
          print("查看全部点击")
        }
      )
    )

    // 纯标题 - ViewModel方式
    TitleKit(
      viewModel: TitleKitVM.titleOnly(title: "统计")
    )

    // 自定义配置的标题 - ViewModel方式
    TitleKit(
      viewModel: TitleKitVM.customStyle(
        title: "自定义标题",
        rightButtonTitle: "更多",
        rightButtonAction: { print("更多点击") }
      )
    )
  }
  .background(Color.gray.opacity(0.1))
}

#Preview("ViewModel方式") {
  VStack(spacing: 20) {
    // 完全使用ViewModel的示例
    TitleKit(
      viewModel: TitleKitVM.withViewAllButton(title: "我的卡片") {
        print("查看全部卡片")
      }
    )

    // 自定义样式的标题
    TitleKit(
      viewModel: TitleKitVM.customStyle(
        title: "重要通知",
        fontSize: 18,
        fontWeight: .bold,
        color: .red,
        rightButtonTitle: "设置",
        rightButtonAction: { print("设置点击") }
      )
    )

    // 纯ViewModel初始化
    TitleKit(
      viewModel: TitleKitVM(
        title: "数据分析",
        rightButtonTitle: "详情",
        rightButtonAction: { print("详情点击") }
      )
    )
  }
  .background(Color.gray.opacity(0.1))
}

#Preview("样式展示") {
  VStack(spacing: 20) {
    // 大标题
    TitleKit(
      viewModel: TitleKitVM.customStyle(
        title: "大标题示例",
        fontSize: 20,
        fontWeight: .heavy,
        color: .blue
      )
    )

    // 小标题
    TitleKit(
      viewModel: TitleKitVM.customStyle(
        title: "小标题示例",
        fontSize: 14,
        fontWeight: .regular,
        color: .gray
      )
    )

    // 禁用按钮的示例
    TitleKit(
      viewModel: {
        let vm = TitleKitVM(
          title: "禁用按钮示例",
          rightButtonTitle: "已禁用",
          rightButtonAction: { print("不会执行") }
        )
        vm.setRightButtonEnabled(false)
        return vm
      }()
    )
  }
  .background(Color.gray.opacity(0.1))
}
