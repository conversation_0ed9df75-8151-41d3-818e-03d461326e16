//
//  CardVM.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/18.
//

import Combine
import SwiftUI

// VM: 负责所有业务逻辑和数据准备
// 它是一个 class 并遵循 ObservableObject，以便未来可以轻松支持动态更新
// 参考现有CardComponentView的实现，保持一致的设计
class CardVM: ObservableObject, Identifiable {

  // MARK: - Output Properties (for the View to use)
  // 这些是已经计算好的、可以直接在 View 中使用的最终数据

  let id: String  // 唯一标识符，用于SwiftUI动画识别
  @Published var bankName: String
  @Published var cardName: String
  @Published var balance: Double
  @Published var currencySymbol: String
  @Published var currencyCode: String
  @Published var isCredit: Bool
  @Published var bankLogo: Data?
  @Published var cover: String
  @Published var isDarkBackground: Bool

  // 信用卡特定属性
  @Published var billDay: Int?
  @Published var dueDay: Int?
  @Published var isFixedDueDay: Bool

  // 编辑模式相关
  @Published var isEditMode: Bool

  // 回调函数
  var onTap: (() -> Void)?
  var getNextBillDate: (() -> Date?)?
  var getNextDueDate: (() -> Date?)?
  var setCover: ((String) -> Void)?
  var setIsDarkBackground: ((Bool) -> Void)?

  // MARK: - 初始化方法

  init(
    id: String = UUID().uuidString,
    bankName: String,
    cardName: String,
    balance: Double,
    currencySymbol: String,
    currencyCode: String = "CNY",
    isCredit: Bool,
    bankLogo: Data? = nil,
    cover: String = "",
    isDarkBackground: Bool = false,
    billDay: Int? = nil,
    dueDay: Int? = nil,
    isFixedDueDay: Bool = true,
    isEditMode: Bool = false,
    onTap: (() -> Void)? = nil,
    getNextBillDate: (() -> Date?)? = nil,
    getNextDueDate: (() -> Date?)? = nil,
    setCover: ((String) -> Void)? = nil,
    setIsDarkBackground: ((Bool) -> Void)? = nil
  ) {
    self.id = id
    self.bankName = bankName
    self.cardName = cardName
    self.balance = balance
    self.currencySymbol = currencySymbol
    self.currencyCode = currencyCode
    self.isCredit = isCredit
    self.bankLogo = bankLogo
    self.cover = cover
    self.isDarkBackground = isDarkBackground
    self.billDay = billDay
    self.dueDay = dueDay
    self.isFixedDueDay = isFixedDueDay
    self.isEditMode = isEditMode
    self.onTap = onTap
    self.getNextBillDate = getNextBillDate
    self.getNextDueDate = getNextDueDate
    self.setCover = setCover
    self.setIsDarkBackground = setIsDarkBackground
  }

  // MARK: - 计算属性

  /// 获取显示的银行名称
  var displayBankName: String {
    return bankName.isEmpty ? (isCredit ? "信用卡" : "储蓄卡") : bankName
  }

  /// 获取文本颜色（根据背景深浅）
  var textColor: Color {
    let baseColor: Color = isDarkBackground ? .white : .cBlack
    return baseColor
  }

  /// 获取距离下一个出账日的天数
  var daysUntilNextBillDate: Int? {
    guard let nextBillDate = getNextBillDate?() else { return nil }
    let calendar = Calendar.current
    let today = calendar.startOfDay(for: Date())
    let billDate = calendar.startOfDay(for: nextBillDate)
    return calendar.dateComponents([.day], from: today, to: billDate).day
  }

  /// 获取距离下一个还款日的天数
  var daysUntilNextDueDate: Int? {
    guard let nextDueDate = getNextDueDate?() else { return nil }
    let calendar = Calendar.current
    let today = calendar.startOfDay(for: Date())
    let dueDate = calendar.startOfDay(for: nextDueDate)
    return calendar.dateComponents([.day], from: today, to: dueDate).day
  }

  /// 出账日显示文本
  var billDayDisplayText: String {
    guard let billDay = billDay else { return "" }
    if let daysUntil = daysUntilNextBillDate {
      if daysUntil == 0 {
        return "今日出账"
      } else if daysUntil > 0 {
        return "距离出账日\(daysUntil)天"
      }
    }
    return "\(billDay)日"
  }

  /// 还款日显示文本
  var dueDayDisplayText: String {
    guard let dueDay = dueDay else { return "" }
    if let daysUntil = daysUntilNextDueDate {
      if daysUntil == 0 {
        return "今日还款"
      } else if daysUntil > 0 {
        return "距离还款日\(daysUntil)天"
      }
    }
    return "\(dueDay)日"
  }

  /// 判断应该显示哪个日期信息（优先显示更紧急的）
  var shouldShowBillDate: Bool {
    guard let billDays = daysUntilNextBillDate,
      let dueDays = daysUntilNextDueDate
    else {
      // 如果只有一个日期有效，则显示出账日（如果存在）
      return billDay != nil
    }

    // 如果两个日期都有效，显示距离更近的那个
    // 如果距离相同，优先显示出账日
    return billDays <= dueDays
  }

  /// 获取要显示的日期文本
  var displayDateText: String {
    return shouldShowBillDate ? billDayDisplayText : dueDayDisplayText
  }

  /// 计算距离指定日期的天数
  func getDaysUntilDate(_ targetDate: Date) -> Int {
    let calendar = Calendar.current
    let today = calendar.startOfDay(for: Date())
    let target = calendar.startOfDay(for: targetDate)
    let days = calendar.dateComponents([.day], from: today, to: target).day ?? 0
    return max(0, days)  // 确保不返回负数
  }

  /// 获取卡片背景
  var cardBackground: some View {
    // 缓存背景类型计算结果
    let coverType = CardCoverHelper.shared.getCoverType(from: cover)

    // 使用图片背景
    let imageName = CardCoverHelper.shared.getCoverImageName(for: coverType)
    return AnyView(
      Image(imageName)
        .resizable()
        //        .aspectRatio(contentMode: .fill)
    )
  }

  // MARK: - 静态工厂方法

  /// 从 CardModel 创建 CardVM
  static func fromCard(
    _ card: CardModel,
    onTap: (() -> Void)? = nil,
    isEditMode: Bool = false,
    setCover: ((String) -> Void)? = nil,
    setIsDarkBackground: ((Bool) -> Void)? = nil
  ) -> CardVM {
    // 根据cover字段获取背景类型和暗色标志
    let coverType = CardCoverHelper.shared.getCoverType(from: card.cover)
    let isDark = CardCoverHelper.shared.isCoverDark(for: coverType)

    return CardVM(
      id: card.id.uuidString,
      bankName: card.bankName,
      cardName: card.name,
      balance: card.balance,
      currencySymbol: card.symbol,
      currencyCode: card.currency,
      isCredit: card.isCredit,
      bankLogo: card.bankLogo,
      cover: card.cover,
      isDarkBackground: isDark,
      billDay: card.billDay,
      dueDay: card.dueDay,
      isFixedDueDay: card.isFixedDueDay,
      isEditMode: isEditMode,
      onTap: onTap,
      getNextBillDate: { card.getNextBillDate() },
      getNextDueDate: { card.getNextDueDate() },
      setCover: setCover,
      setIsDarkBackground: setIsDarkBackground
    )
  }
}
